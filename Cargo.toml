[package]
name = "HenrikDevAPI"
version = "4.3.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
axum = { version = "0.8.4", features = ["json", "macros"] }
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1"
log = "0.4"
env_logger = "0.11"
futures = { version = "0.3", default-features = false }
reqwest = { version = "0.12.4", features = ["json"] }
lazy_static = "1.4"
mongodb = { version = "3.2.4", features = ["zstd-compression"] }
dotenv = "0.15"
brotli = "8"
valorant_api_official = "0.2.0"
#valorant_api = "0.3"
valorant_api = { git = "https://gitlab.com/valorant-api/rust-valorant-api.git", branch = "temp_master" }
valorant-assets-api = "0.1.5"
redis = { version = "0.32.3", features = ["tokio-comp", "r2d2", "cluster"] }
deadpool-redis = "0.21.1"
regex = "1"
serde_path_to_error = "0.1"
chrono = "0.4.31"
urlencoding = "2.1.3"
uuid = { version = "1.17.0", features = ["v4", "serde"] }
jsonwebtoken = "9.3.0"
mimalloc = "0.1.43"
sqlx = { version = "0.8.6", features = ["postgres", "chrono", "runtime-tokio-native-tls"] }
base64 = "0.22.1"
zstd = "0.13.3"
minio-rsc = { git = "https://github.com/Henrik-3/minio-rsc-b2.git" }
utoipa = "5.4.0"
bytes = "1.10.1"
once_cell = "1.21.3"
arc-swap = "1.7.1"
utoipa-swagger-ui = { version = "9.0.2", features = ["axum"] }
scraper = "0.24.0"

[[bin]]
name = "api_server"
path = "main.rs"

[profile.dev]
overflow-checks = true

[profile.release]
lto = "thin"
strip = true
debug = false
opt-level = 2
