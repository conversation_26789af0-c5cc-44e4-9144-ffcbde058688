use crate::methods::utils::{ get_match_b2, RateLimiting };
use crate::structs::errors::{ error_handler, Error<PERSON><PERSON>, SendError, json_response };
use crate::structs::parser::{ parse_match_v2, parse_match_v4 };
use crate::structs::paths::{ MatchV2Path, MatchV4Path };
use crate::structs::responses::{ MatchesV2Response, MatchesV4Response };
use crate::{ check_affinity, get_db_hdd, AppState };
use axum::body::Body;
use axum::extract::{ Path, State };
use axum::response::Response;
use axum::Extension;
use futures::future::join_all;
use mongodb::bson::{ doc, from_document, Document };
use std::sync::Arc;
use uuid::Uuid;
use valorant_api::response_types::matchdetails_v1::MatchDetailsV1;

#[utoipa::path(
	get,
	path = "/valorant/v2/match/{match_id}",
	tag = "valorant",
	params(
		("match_id" = String, Path, description = "Match UUID")
	),
	responses(
		(status = 200, description = "Match details retrieved successfully", body = MatchesV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Match not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn match_v2(Path(path): Path<MatchV2Path>, State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>) -> Response {
	let validate_uuid = Uuid::parse_str(&path.match_id);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let mut match_fetch: Option<MatchDetailsV1> = None;
	let match_ = get_match_b2(&client, redis.clone(), path.match_id.clone(), "eu".to_string()).await;
	if !match_.error {
		match_fetch = Some(match_.data.clone().unwrap());
	}
	if !match_.error && !match_.is_from_db {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}

	let mut full_match: Option<MatchDetailsV1> = None;
	let mut affinity: Option<String> = None;

	if let Some(extracted) = match_fetch {
		let pod = extracted.match_info.game_pod_id.clone();
		full_match = Some(extracted);
		affinity = Some(match pod {
			x if x.contains("ap") => String::from("ap"),
			x if x.contains("eu") => String::from("eu"),
			x if x.contains("kr") => String::from("kr"),
			x if x.contains("na") => String::from("na"),
			x if x.contains("latam") => String::from("latam"),
			x if x.contains("br") => String::from("br"),
			_ => String::from("eu"),
		});
	} else {
		let match_id = path.match_id.clone();
		let check_all_regions = join_all(
			vec![
				get_match_b2(&client, redis.clone(), match_id.clone(), "ap".to_string()),
				get_match_b2(&client, redis.clone(), match_id.clone(), "eu".to_string()),
				get_match_b2(&client, redis.clone(), match_id.clone(), "kr".to_string()),
				get_match_b2(&client, redis.clone(), match_id, "na".to_string())
			]
		).await;
		rl.background_requests.fetch_add(4, std::sync::atomic::Ordering::SeqCst);
		let successful_region = check_all_regions.iter().find(|x| !x.error);
		if let Some(region) = successful_region {
			full_match = Some(region.data.clone().unwrap());
			affinity = Some(region.affinity.clone());
		} else {
			return error_handler(vec![ErrorCodes::MatchNotFound]);
		}
	}

	if full_match.is_none() {
		return error_handler(vec![ErrorCodes::MatchNotFound]);
	}

	let format = parse_match_v2(full_match.unwrap(), &affinity.unwrap()).await;

	let response = MatchesV2Response {
		status: 200,
		data: format,
	};
	json_response(&response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v4/match/{affinity}/{match_id}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("match_id" = String, Path, description = "Match UUID")
	),
	responses(
		(status = 200, description = "Match details retrieved successfully", body = MatchesV4Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Match not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn match_v4(Path(path): Path<MatchV4Path>, State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>) -> Response {
	let validate_uuid = Uuid::parse_str(&path.match_id);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let validate_affinity = check_affinity(&path.affinity);
	if !validate_affinity {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}

	let client = app_state.client.clone();
	let rl = extension.clone();

	let mut full_match: Option<MatchDetailsV1> = None;
	let match_ = get_match_b2(&client, app_state.redis.clone(), path.match_id.clone(), path.affinity.clone()).await;
	if match_.error {
		return error_handler(vec![ErrorCodes::MatchNotFound]);
	} else {
		full_match = Some(match_.data.clone().unwrap());
	}
	if !match_.error && !match_.is_from_db {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}

	if full_match.is_none() {
		return error_handler(vec![ErrorCodes::MatchNotFound]);
	}

	let format = parse_match_v4(full_match.unwrap(), &path.affinity.clone()).await;

	let response = MatchesV4Response {
		status: 200,
		data: format,
	};
	json_response(&response, 200)
}
