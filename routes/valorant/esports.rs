use std::sync::Arc;
use std::sync::atomic::Ordering;
use axum::Extension;
use axum::extract::{ Query, State };
use axum::response::Response;
use reqwest::header::HeaderMap;
use valorant_api::response_types::esports_service_leagues::EsportsLeagues;
use valorant_api::response_types::esports_service_schedule::{ EsportsSchedule, EsportsScheduleMetaEvent };
use valorant_api::response_types::esports_service_vods::EsportsVod;
use crate::{ AppState };
use crate::methods::utils::RateLimiting;
use crate::structs::errors::{ error_handler, json_response, ErrorCodes, SendError };
use crate::structs::http_clients::{ redis_fetch, RedisFetchOptions };
use crate::structs::paths::EsportsScheduleQuery;
use crate::structs::responses::{
	EsportsV1Data,
	EsportsV1DataLeague,
	EsportsV1DataMatch,
	EsportsV1DataMatchGameType,
	EsportsV1DataMatchTeams,
	EsportsV1DataMatchTeamsRecord,
	EsportsV1DataTournament,
	EsportsV1Response,
};

#[utoipa::path(
	get,
	path = "/valorant/v1/esports/schedule",
	tag = "valorant",
	params(
		("region" = Option<String>, Query, description = "Region filter (optional)")
	),
	responses(
		(status = 200, description = "Esports schedule retrieved successfully", body = EsportsV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Schedule not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn esports_schedules_v1(
	Query(query): Query<EsportsScheduleQuery>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let mut headers = HeaderMap::new();
	headers.insert("x-api-key", "0TvQnueqKa5mxJntVWt0w4LpLfEkrV1Ta8rQBb9Z".parse().unwrap());
	let rl = extension.as_ref();
	//let redis = app_state.pool.clone();
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mut ttls: Vec<i16> = vec![];
	let leagues = redis_fetch::<EsportsLeagues>(RedisFetchOptions {
		url: "https://esports-api.service.valorantesports.com/persisted/val/getLeagues?hl=en-US&sport=val".to_string(),
		method: "GET".to_string(),
		headers: headers.clone(),
		store: "esports;leagues".to_string(),
		redis_client: Some(conn),
		..RedisFetchOptions::default()
	}).await;
	if leagues.is_err() {
		return error_handler(vec![ErrorCodes::FetchingResource]);
	}
	let leagues = leagues.unwrap();
	ttls.push(leagues.ttl);
	if !leagues.is_from_redis {
		rl.background_requests.fetch_add(1, Ordering::SeqCst);
	}
	let possible_regions = leagues
		.clone()
		.data.data.leagues.iter()
		.map(|x| x.region.to_lowercase().replace(" ", "_"))
		.collect::<Vec<String>>();
	let possible_leagues = leagues
		.clone()
		.data.data.leagues.iter()
		.map(|x| x.slug.to_lowercase())
		.collect::<Vec<String>>();
	let leagues_string = if let Some(league) = query.league.clone() {
		let split = league
			.split(",")
			.map(|x| x.trim().to_lowercase())
			.collect::<Vec<String>>();
		for league_ in &split {
			if !possible_leagues.contains(&league_.trim().to_lowercase()) {
				return error_handler(vec![ErrorCodes::InvalidLeague]);
			}
		}
		leagues.data.data.leagues
			.clone()
			.into_iter()
			.filter(|x| split.contains(&x.slug.to_lowercase()))
			.map(|x| x.id)
			.collect::<Vec<String>>()
			.join(",")
	} else {
		leagues.data.data.leagues
			.clone()
			.into_iter()
			.map(|x| x.id)
			.collect::<Vec<String>>()
			.join(",")
	};
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let schedule = redis_fetch::<EsportsSchedule>(RedisFetchOptions {
		url: format!("https://esports-api.service.valorantesports.com/persisted/val/getSchedule?hl=en-US&sport=val&leagueId={}", leagues_string),
		method: "GET".to_string(),
		headers: headers.clone(),
		store: format!("esports;schedule;{}", leagues_string),
		redis_client: Some(conn),
		..RedisFetchOptions::default()
	}).await;
	if schedule.is_err() {
		return error_handler(vec![ErrorCodes::FetchingResource]);
	}
	let schedule = schedule.unwrap();
	ttls.push(schedule.ttl);
	if !schedule.is_from_redis {
		rl.background_requests.fetch_add(1, Ordering::SeqCst);
	}
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let vods = redis_fetch::<EsportsVod>(RedisFetchOptions {
		url: format!("https://esports-api.service.valorantesports.com/persisted/val/getVods?hl=en-US&sport=val&leagueId={}", leagues_string),
		method: "GET".to_string(),
		headers: headers.clone(),
		store: "esports;vods".to_string(),
		redis_client: Some(conn),
		..RedisFetchOptions::default()
	}).await;
	if vods.is_err() {
		return error_handler(vec![ErrorCodes::FetchingResource]);
	}
	let vods = vods.unwrap();
	ttls.push(vods.ttl);
	if !vods.is_from_redis {
		rl.background_requests.fetch_add(1, Ordering::SeqCst);
	}
	let mut schedules = schedule.data.data.schedule.events;
	if let Some(region) = query.region.clone() {
		if !possible_regions.contains(&&region.to_lowercase()) {
			return error_handler(vec![ErrorCodes::InvalidRegion]);
		}
		schedules = schedules
			.into_iter()
			.filter(|x| x.league.region.to_lowercase() == region.to_lowercase())
			.collect::<Vec<EsportsScheduleMetaEvent>>();
	}

	ttls.sort();
	rl.redis_cache_ttl.store(*ttls.first().unwrap() as isize, Ordering::SeqCst);

	let vods = vods.data.data.schedule.events;
	let schedule_format = schedules
		.iter()
		.map(|x| {
			let vod = vods.iter().find(|y| y.r#match.id == (if let Some(match_) = x.r#match.clone() { match_.id.clone() } else { "".to_string() }));
			EsportsV1Data {
				date: x.start_time.to_rfc3339(),
				state: x.state.to_string(),
				type_: x.r#type.to_string(),
				vod: if let Some(vod) = vod {
					Some(format!("https://youtu.be/{}", vod.games.first().unwrap().vods.first().unwrap().parameter))
				} else {
					None
				},
				league: EsportsV1DataLeague {
					name: x.league.name.clone(),
					region: x.league.region.clone(),
					identifier: x.league.slug.clone(),
					icon: x.league.image.clone(),
				},
				tournament: EsportsV1DataTournament {
					name: x.tournament.split.name.clone(),
					season: x.tournament.season.name.clone(),
				},
				match_: EsportsV1DataMatch {
					id: if let Some(match_) = x.r#match.clone() {
						Some(match_.id.clone())
					} else {
						None
					},
					teams: if let Some(match_) = x.r#match.clone() {
						match_.teams
							.iter()
							.map(|y| {
								let result = y.result.clone();
								let record = y.record.clone();
								EsportsV1DataMatchTeams {
									name: y.name.clone(),
									code: y.code.clone(),
									icon: y.image.clone(),
									has_won: if let Some(result) = result.clone() {
										if let Some(outcome) = result.outcome.clone() { outcome == "win" } else { false }
									} else {
										false
									},
									game_wins: if let Some(result) = result {
										result.game_wins as i32
									} else {
										0
									},
									record: EsportsV1DataMatchTeamsRecord {
										wins: if let Some(record) = record.clone() {
											record.wins as i32
										} else {
											0
										},
										losses: if let Some(record) = record.clone() {
											record.losses as i32
										} else {
											0
										},
									},
								}
							})
							.collect::<Vec<EsportsV1DataMatchTeams>>()
					} else {
						vec![]
					},
					game_type: {
						let strategy = if let Some(match_) = x.r#match.clone() { Some(match_.strategy.clone()) } else { None };
						EsportsV1DataMatchGameType {
							type_: if let Some(strategy) = strategy.clone() {
								Some(strategy.r#type)
							} else {
								None
							},
							count: if let Some(strategy) = strategy {
								Some(strategy.count as i32)
							} else {
								None
							},
						}
					},
				},
			}
		})
		.collect::<Vec<EsportsV1Data>>();
	json_response(
		&(EsportsV1Response {
			status: 200,
			data: schedule_format,
		}),
		200
	)
}
