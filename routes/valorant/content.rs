use std::sync::Arc;
use std::sync::atomic::Ordering;
use axum::Extension;
use axum::extract::{ Query, State };
use axum::response::Response;
use reqwest::header::HeaderMap;
use valorant_api_official::response_types::content_v1::ContentV1;
use crate::structs::errors::{ error_handler, ErrorCodes, SendError, json_response };
use crate::structs::http_clients::{ redis_fetch, RedisFetchOptions };
use crate::structs::paths::ContentQuery;
use crate::structs::responses::ContentV1Response;
use crate::{ AppState, RIOT_TOKEN, VALORANT_CONTENT_LOCALS };
use crate::methods::utils::RateLimiting;

#[utoipa::path(
	get,
	path = "/valorant/v1/content",
	tag = "valorant",
	params(
		("locale" = Option<String>, Query, description = "Locale code (e.g., en-US, de-DE) - optional")
	),
	responses(
		(status = 200, description = "Content retrieved successfully", body = ContentV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Content not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_content_v1(Query(query): Query<ContentQuery>, State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>) -> Response {
	let mut header = HeaderMap::new();
	header.insert("X-Riot-Token", RIOT_TOKEN.parse().unwrap());
	let rl = extension.as_ref();
	//let redis = app_state.pool.clone();
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	if query.locale.is_some() {
		let locale = VALORANT_CONTENT_LOCALS.into_iter().find(|x| x.to_lowercase() == query.locale.clone().unwrap().to_lowercase());
		if locale.is_none() {
			return error_handler(vec![ErrorCodes::InvalidLocaleContent]);
		}
		let fetch = redis_fetch::<ContentV1>(RedisFetchOptions {
			url: format!("https://eu.api.riotgames.com/val/content/v1/contents?locale={}", locale.unwrap()),
			headers: header,
			user_agent: "ValorantLabs".to_string(),
			store: format!("content;locale;{}", locale.unwrap().to_lowercase()).to_string(),
			redis_client: Some(conn),
			..RedisFetchOptions::default()
		}).await;
		if fetch.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		let unwrap_content = fetch.unwrap();
		rl.redis_cache_ttl.store(unwrap_content.ttl as isize, Ordering::SeqCst);
		if !unwrap_content.is_from_redis {
			rl.background_requests.fetch_add(1, Ordering::SeqCst);
		}
		let response = ContentV1Response {
			status: 200,
			data: unwrap_content.data,
		};
		return json_response(&response, 200);
	}
	let fetch = redis_fetch::<ContentV1>(RedisFetchOptions {
		url: "https://eu.api.riotgames.com/val/content/v1/contents?locale=en-US".to_string(),
		headers: header,
		user_agent: "ValorantLabs".to_string(),
		store: "content;locale;en-us".to_string(),
		redis_client: Some(conn),
		..RedisFetchOptions::default()
	}).await;
	if fetch.is_err() {
		return error_handler(vec![ErrorCodes::FetchingResource]);
	}
	let unwrap_content = fetch.unwrap();
	rl.redis_cache_ttl.store(unwrap_content.ttl as isize, Ordering::SeqCst);
	if !unwrap_content.is_from_redis {
		rl.background_requests.fetch_add(1, Ordering::SeqCst);
	}
	let response = ContentV1Response {
		status: 200,
		data: unwrap_content.data,
	};
	json_response(&response, 200)
}
