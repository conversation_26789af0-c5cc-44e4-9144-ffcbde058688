use crate::methods::utils::RateLimiting;
use crate::structs::errors::{ error_handler, json_response, ErrorCodes, SendError };
use crate::structs::helper::{ get_valorant_account_by_id, get_valorant_account_by_name, sanitize_name };
use crate::structs::paths::{ AccountV1ByIDPath, AccountV1Path, AccountV1Query, AccountV2ByIDPath, AccountV2Path, AccountV2Query };
use crate::structs::responses::{ AccountV1Data, AccountV1DataCard, AccountV1Response, AccountV2Data, AccountV2Response };
use crate::{ AppState };
use axum::extract::{ Path, Query, State };
use axum::response::Response;
use axum::Extension;
use mongodb::bson::DateTime;
use std::sync::atomic::Ordering;
use std::sync::Arc;
use uuid::Uuid;

#[utoipa::path(
	get,
	path = "/valorant/v1/account/{name}/{tag}",
	tag = "valorant",
	params(
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag"),
		("force" = Option<bool>, Query, description = "Bypass cache and refresh (optional)")
	),
	responses(
		(status = 200, description = "Account data retrieved successfully", body = AccountV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_account_v1(
	Query(query): Query<AccountV1Query>,
	Path(path): Path<AccountV1Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let sanitized_name = sanitize_name(&path.name);
	let sanitized_tag = sanitize_name(&path.tag);
	let force = query.force.unwrap_or(false);

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, redis, &sanitized_name, &sanitized_tag, force, true).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, Ordering::SeqCst);
	let a = account_wrapper.data;
	let chrono_ = chrono::DateTime::from_timestamp_millis(a.updated_at.timestamp_millis()).unwrap();
	let min_since = chrono::Utc::now().signed_duration_since(chrono_).num_minutes();
	let last_text = format!("{} minutes ago", min_since);
	let cache_ttl = (a.updated_at.timestamp_millis() + 3600 * 1000 - DateTime::now().timestamp_millis()) / 1000;

	rl.redis_cache_ttl.store(cache_ttl as isize, Ordering::SeqCst);
	let response = AccountV1Response {
		status: 200,
		data: AccountV1Data {
			puuid: a.puuid,
			region: a.region,
			account_level: a.account_level,
			name: a.name,
			tag: a.tag,
			card: AccountV1DataCard {
				small: format!("https://media.valorant-api.com/playercards/{}/smallart.png", a.card),
				large: format!("https://media.valorant-api.com/playercards/{}/largeart.png", a.card),
				wide: format!("https://media.valorant-api.com/playercards/{}/wideart.png", a.card),
				id: a.card,
			},
			last_update: last_text,
			last_update_raw: a.updated_at.timestamp_millis() / 1000,
		},
	};
	json_response(&response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v1/by-puuid/account/{puuid}",
	tag = "valorant",
	params(
		("puuid" = String, Path, description = "Player UUID"),
		("force" = Option<bool>, Query, description = "Bypass cache and refresh (optional)")
	),
	responses(
		(status = 200, description = "Account data retrieved successfully", body = AccountV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_account_by_id_v1(
	Query(query): Query<AccountV1Query>,
	Path(path): Path<AccountV1ByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let force = query.force.unwrap_or(false);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, force, true).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, Ordering::SeqCst);
	let a = account_wrapper.data;
	let chrono_ = chrono::DateTime::from_timestamp_millis(a.updated_at.timestamp_millis()).unwrap();
	let min_since = chrono::Utc::now().signed_duration_since(chrono_).num_minutes();
	let last_text = format!("{} minutes ago", min_since);
	let cache_ttl = (a.updated_at.timestamp_millis() + 3600 * 1000 - DateTime::now().timestamp_millis()) / 1000;

	rl.redis_cache_ttl.store(cache_ttl as isize, Ordering::SeqCst);
	let response = AccountV1Response {
		status: 200,
		data: AccountV1Data {
			puuid: a.puuid,
			region: a.region,
			account_level: a.account_level,
			name: a.name,
			tag: a.tag,
			card: AccountV1DataCard {
				small: format!("https://media.valorant-api.com/playercards/{}/smallart.png", a.card),
				large: format!("https://media.valorant-api.com/playercards/{}/largeart.png", a.card),
				wide: format!("https://media.valorant-api.com/playercards/{}/wideart.png", a.card),
				id: a.card,
			},
			last_update: last_text,
			last_update_raw: a.updated_at.timestamp_millis() / 1000,
		},
	};
	json_response(&response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/account/{name}/{tag}",
	tag = "valorant",
	params(
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag"),
		("force" = Option<bool>, Query, description = "Bypass cache and refresh (optional)")
	),
	responses(
		(status = 200, description = "Account data retrieved successfully", body = AccountV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_account_v2(
	Query(query): Query<AccountV2Query>,
	Path(path): Path<AccountV2Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let sanitized_name = sanitize_name(&path.name);
	let sanitized_tag = sanitize_name(&path.tag);
	let force = query.force.unwrap_or(false);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &sanitized_name, &sanitized_tag, force, true).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, Ordering::SeqCst);
	let a = account_wrapper.data;
	let cache_ttl = (a.updated_at.timestamp_millis() + 3600 * 1000 - DateTime::now().timestamp_millis()) / 1000;

	rl.redis_cache_ttl.store(cache_ttl as isize, Ordering::SeqCst);
	let response = AccountV2Response {
		status: 200,
		data: AccountV2Data {
			puuid: a.puuid,
			region: a.region,
			account_level: a.account_level,
			name: a.name,
			tag: a.tag,
			card: a.card,
			title: a.title,
			platforms: a.platforms,
			updated_at: a.updated_at.try_to_rfc3339_string().unwrap(),
		},
	};
	json_response(&response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/by-puuid/account/{puuid}",
	tag = "valorant",
	params(
		("puuid" = String, Path, description = "Player UUID"),
		("force" = Option<bool>, Query, description = "Bypass cache and refresh (optional)")
	),
	responses(
		(status = 200, description = "Account data retrieved successfully", body = AccountV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_account_by_id_v2(
	Query(query): Query<AccountV2Query>,
	Path(path): Path<AccountV2ByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let force = query.force.unwrap_or(false);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, force, true).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, Ordering::SeqCst);
	let a = account_wrapper.data;
	let cache_ttl = (a.updated_at.timestamp_millis() + 3600 * 1000 - DateTime::now().timestamp_millis()) / 1000;

	rl.redis_cache_ttl.store(cache_ttl as isize, Ordering::SeqCst);
	let response = AccountV2Response {
		status: 200,
		data: AccountV2Data {
			puuid: a.puuid,
			region: a.region,
			account_level: a.account_level,
			name: a.name,
			tag: a.tag,
			card: a.card,
			title: a.title,
			platforms: a.platforms,
			updated_at: a.updated_at.try_to_rfc3339_string().unwrap(),
		},
	};
	json_response(&response, 200)
}
