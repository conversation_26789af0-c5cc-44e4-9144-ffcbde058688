use std::sync::Arc;
use axum::body::Body;
use axum::extract::{ Path, Query, State };
use axum::response::Response;
use crate::{
	error_handler,
	get_db,
	structs::{ paths::{ WebsitePath, WebsiteQuery }, responses::{ WebsiteV1Data, WebsiteV1Response }, errors::{ SendError, json_response } },
	ErrorCodes,
	VALORANT_WEBSITE_CATEGORIES,
	VALORANT_WEBSITE_COUNTRIES,
	AppState,
};

use futures::{ TryStreamExt };
use mongodb::{ bson::doc };
use crate::structs::website::{ RitoMobileNewsElement };

#[utoipa::path(
	get,
	path = "/valorant/v1/website/{country_code}",
	tag = "valorant",
	params(
		("country_code" = String, Path, description = "Country code (e.g., en-us, de-de)"),
		("category" = Option<String>, Query, description = "Category filter (optional)")
	),
	responses(
		(status = 200, description = "Website content retrieved successfully", body = WebsiteV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Content not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
#[allow(non_snake_case)]
pub async fn Website(Path(path): Path<WebsitePath>, Query(query): Query<WebsiteQuery>, State(app_state): State<Arc<AppState>>) -> Response {
	let ccode = path.country_code.to_lowercase();
	if !VALORANT_WEBSITE_COUNTRIES.contains(&ccode.as_str()) {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	if query.category.is_some() && !VALORANT_WEBSITE_CATEGORIES.contains(&query.category.as_ref().unwrap().as_str()) {
		return error_handler(vec![ErrorCodes::InvalidWebsiteCategory]);
	}
	let client = app_state.client.clone();
	if query.category.is_some() {
		let category = query.category.as_ref().unwrap().clone();
		let articles_fetch = get_db::<RitoMobileNewsElement>(&client, "website", None)
			.find(doc! { "cms.locale": &ccode, "category.id": category.clone() })
			.sort(doc! { "publishedAt": -1 }).await;
		if articles_fetch.is_err() {
			return error_handler(vec![ErrorCodes::InternalError]);
		}
		let articles: Vec<RitoMobileNewsElement> = articles_fetch.unwrap().try_collect().await.unwrap();
		let response_data: Vec<WebsiteV1Data> = articles
			.iter()
			.map(|x| WebsiteV1Data {
				banner_url: Some(x.feature_image.url.clone()),
				category: {
					let headline = x.headline.to_lowercase();
					if headline.contains("patch") {
						"patch_notes".to_string()
					} else {
						x.category.id.clone()
					}
				},
				date: x.published_at.clone().try_to_rfc3339_string().unwrap(),
				external_link: if x.action.type_ != "weblink" {
					Some(x.action.url.clone())
				} else {
					None
				},
				title: x.headline.clone(),
				description: x.description.clone(),
				url: x.action.url.clone(),
			})
			.collect();
		let response = WebsiteV1Response {
			status: 200,
			data: response_data,
		};
		return json_response(&response, 200);
	}
	let articles_fetch = get_db::<RitoMobileNewsElement>(&client, "website", None)
		.find(doc! { "cms.locale": &ccode })
		.sort(doc! { "publishedAt": -1 }).await;
	if articles_fetch.is_err() {
		return error_handler(vec![ErrorCodes::InternalError]);
	}
	let articles: Vec<RitoMobileNewsElement> = articles_fetch.unwrap().try_collect().await.unwrap();
	let response_data: Vec<WebsiteV1Data> = articles
		.iter()
		.map(|x| WebsiteV1Data {
			banner_url: Some(x.feature_image.url.clone()),
			category: {
				let headline = x.headline.to_lowercase();
				if headline.contains("patch") {
					"patch_notes".to_string()
				} else {
					x.category.id.clone()
				}
			},
			date: x.published_at.clone().try_to_rfc3339_string().unwrap(),
			external_link: if x.action.type_ != "weblink" {
				Some(x.action.url.clone())
			} else {
				None
			},
			title: x.headline.clone(),
			url: x.action.url.clone(),
			description: None,
		})
		.collect();
	json_response(
		&(WebsiteV1Response {
			status: 200,
			data: response_data,
		}),
		200
	)
}

pub async fn website_rito_mobile(Path(path): Path<WebsitePath>, Query(query): Query<WebsiteQuery>) -> Response {
	let ccode = path.country_code.to_lowercase();
	if !VALORANT_WEBSITE_COUNTRIES.contains(&ccode.as_str()) {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	if query.category.is_some() && !VALORANT_WEBSITE_CATEGORIES.contains(&query.category.as_ref().unwrap().as_str()) {
		return error_handler(vec![ErrorCodes::InvalidWebsiteCategory]);
	}
	let response = WebsiteV1Response {
		status: 200,
		data: vec![],
	};
	return json_response(&response, 200);
}
