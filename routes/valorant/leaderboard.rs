use std::sync::Arc;
use axum::Extension;
use axum::extract::{ Path, Query, State };
use axum::response::Response;
use mongodb::bson::{ doc, Document, from_document };
use valorant_api::response_types::leaderboard_v1::LeaderboardV1;
use futures::stream::TryStreamExt;
use uuid::Uuid;
use crate::{ AppState, check_affinity, check_platform, get_db };
use crate::methods::utils::RateLimiting;
use crate::structs::database::{ LeaderboardMetadataDB, LeaderboardPlayerDB };
use crate::structs::errors::{ error_handler, ErrorCodes, SendError, json_response };
use crate::structs::helper::{ get_c_season, get_season_by_id, get_short_ids, get_valorant_account_by_id };
use crate::structs::http_clients::{ fetch_ipv6, FetchIPv6Options };
use crate::structs::parser::{ parse_v3_leaderboard, parse_v3_leaderboard_from_api };
use crate::structs::paths::{ LeaderboardV1Path, LeaderboardV1Query, LeaderboardV2Path, LeaderboardV2Query, LeaderboardV3Path, LeaderboardV3Query };
use crate::structs::responses::{ LeaderboardV1DirectFetchResponse, LeaderboardV1Response, LeaderboardV2Response, LeaderboardV3Response, Pagination };

#[utoipa::path(
	get,
	path = "/valorant/v1/leaderboard/{affinity}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("season" = Option<String>, Query, description = "Season ID (optional)"),
		("name" = Option<String>, Query, description = "Player name to search for (optional)"),
		("tag" = Option<String>, Query, description = "Player tag to search for (optional)")
	),
	responses(
		(status = 200, description = "Leaderboard retrieved successfully", body = Value),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Leaderboard not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn leaderboard_v1(
	Query(query): Query<LeaderboardV1Query>,
	Path(path): Path<LeaderboardV1Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}

	let mut db_query = doc! {};
	let c_season = get_c_season().await.to_string();

	let season = if let Some(q) = query.season.clone() {
		let seasons = get_short_ids().await;
		let search = seasons.iter().find(|&x| x.short_id == q);
		if search.is_none() {
			return error_handler(vec![ErrorCodes::InvalidSeason]);
		}
		let season = search.unwrap().season.clone();
		db_query.insert("season", season.as_str());
		season
	} else {
		db_query.insert("season", c_season.as_str());
		c_season.clone()
	};

	let name = query.name.clone();
	let tag = query.tag.clone();
	let puuid = query.puuid.clone();
	if name.is_none() && tag.is_some() {
		return error_handler(vec![ErrorCodes::InvalidNameQuery]);
	}
	if tag.is_none() && name.is_some() {
		return error_handler(vec![ErrorCodes::InvalidTagQuery]);
	}

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	if name.is_some() && tag.is_some() && season == c_season {
		let fetch_current_leaderboard = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!(
				"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}?size=50&query={}",
				if path.affinity == "br" || path.affinity == "latam" {
					"na"
				} else {
					&*path.affinity
				},
				path.affinity,
				c_season,
				name.clone().unwrap()
			),
			..FetchIPv6Options::default()
		}).await;
		if fetch_current_leaderboard.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		let current_leaderboard = fetch_current_leaderboard.unwrap();
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		let players = current_leaderboard.data.players.unwrap_or(vec![]);
		let player = players
			.iter()
			.find(|&x| { x.game_name.to_lowercase() == name.clone().unwrap().to_lowercase() && x.tag_line.to_lowercase() == tag.clone().unwrap().to_lowercase() });
		if player.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player = player.unwrap();
		let response = LeaderboardV1DirectFetchResponse {
			status: 200,
			data: vec![player.to_owned()],
		};
		return json_response(&response, 200);
	} else if name.is_some() && tag.is_some() && season != c_season {
		let query =
			doc! {
            "affinity": path.affinity.clone(),
            "season": season,
            "player.gameName": name.clone().unwrap(),
            "player.tagLine": tag.clone().unwrap()
        };
		let get_from_db = get_db::<Document>(&client, "leaderboard_players", None)
			.find(query)
			.sort(doc! { "player.leaderboardRank": 1 })
			.limit(1000).await;
		if get_from_db.is_err() {
			return error_handler(vec![ErrorCodes::DatabaseError]);
		}
		let mut collected = vec![];
		let mut db = get_from_db.unwrap();
		while let Some(player) = db.try_next().await.unwrap() {
			let player: LeaderboardPlayerDB = from_document(player).unwrap();
			collected.push(player.player);
		}
		let response = LeaderboardV1Response {
			status: 200,
			data: collected,
		};
		return json_response(&response, 200);
	} else if puuid.is_some() && season == c_season {
		let validate_uuid = Uuid::parse_str(&puuid.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let account = get_valorant_account_by_id(&client, redis, &puuid.clone().unwrap(), false, false).await;
		if account.is_err() {
			return error_handler(vec![account.err().unwrap()]);
		}
		let account = account.unwrap();
		rl.background_requests.fetch_add(account.background_requests, std::sync::atomic::Ordering::SeqCst);
		let account = account.data;

		let fetch_current_leaderboard = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!(
				"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}?size=50&query={}",
				if path.affinity == "br" || path.affinity == "latam" {
					"na"
				} else {
					&*path.affinity
				},
				path.affinity,
				c_season,
				account.name
			),
			..FetchIPv6Options::default()
		}).await;
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		if fetch_current_leaderboard.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		let current_leaderboard = fetch_current_leaderboard.unwrap();
		let players = current_leaderboard.data.players.unwrap_or(vec![]);
		let player = players.iter().find(|&x| { x.game_name.to_lowercase() == account.name.to_lowercase() && x.tag_line.to_lowercase() == account.tag.to_lowercase() });
		if player.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player = player.unwrap().to_owned();
		let response = LeaderboardV1DirectFetchResponse {
			status: 200,
			data: vec![player],
		};
		return json_response(&response, 200);
	} else if puuid.is_some() && season != c_season {
		let validate_uuid = Uuid::parse_str(&puuid.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let query = doc! {
            "affinity": path.affinity.clone(),
            "season": season,
            "player.puuid": puuid.clone().unwrap(),
        };
		let get_from_db = get_db::<Document>(&client, "leaderboard_players", None)
			.find(query)
			.sort(doc! { "player.leaderboardRank": 1 })
			.limit(1000).await;
		if get_from_db.is_err() {
			return error_handler(vec![ErrorCodes::DatabaseError]);
		}
		let mut collected = vec![];
		let mut db = get_from_db.unwrap();
		while let Some(player) = db.try_next().await.unwrap() {
			let player: LeaderboardPlayerDB = from_document(player).unwrap();
			collected.push(player.player);
		}
		let response = LeaderboardV1Response {
			status: 200,
			data: collected,
		};
		return json_response(&response, 200);
	}

	let query = doc! {
        "affinity": path.affinity.clone(),
        "season": season,
    };
	let get_from_db = get_db::<Document>(&client, "leaderboard_players", None)
		.find(query)
		.sort(doc! { "player.leaderboardRank": 1 })
		.limit(1000).await;
	if get_from_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mut collected = vec![];
	let mut db = get_from_db.unwrap();
	while let Some(player) = db.try_next().await.unwrap() {
		let player: LeaderboardPlayerDB = from_document(player).unwrap();
		collected.push(player.player);
	}
	let response = LeaderboardV1Response {
		status: 200,
		data: collected,
	};
	json_response(&response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/leaderboard/{affinity}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("season" = Option<String>, Query, description = "Season ID (optional)"),
		("name" = Option<String>, Query, description = "Player name to search for (optional)"),
		("tag" = Option<String>, Query, description = "Player tag to search for (optional)"),
		("puuid" = Option<String>, Query, description = "Player UUID to search for (optional)")
	),
	responses(
		(status = 200, description = "Leaderboard retrieved successfully", body = LeaderboardV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Leaderboard not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn leaderboard_v2(
	Query(query): Query<LeaderboardV2Query>,
	Path(path): Path<LeaderboardV2Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}

	let mut db_query = doc! {};
	let c_season = get_c_season().await.to_string();

	let season = if let Some(q) = query.season.clone() {
		let seasons = get_short_ids().await;
		let search = seasons.iter().find(|&x| x.short_id == q);
		if search.is_none() {
			return error_handler(vec![ErrorCodes::InvalidSeason]);
		}
		let season = search.unwrap().season.clone();
		db_query.insert("season", season.as_str());
		season
	} else {
		db_query.insert("season", c_season.as_str());
		c_season.clone()
	};

	let name = query.name.clone();
	let tag = query.tag.clone();
	let puuid = query.puuid.clone();
	if name.is_none() && tag.is_some() {
		return error_handler(vec![ErrorCodes::InvalidNameQuery]);
	}
	if tag.is_none() && name.is_some() {
		return error_handler(vec![ErrorCodes::InvalidTagQuery]);
	}

	let client = app_state.client.clone();
	let rl = extension.clone();

	if name.is_some() && tag.is_some() && season == c_season {
		let fetch_current_leaderboard = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!(
				"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}?size=50&query={}",
				if path.affinity == "br" || path.affinity == "latam" {
					"na"
				} else {
					&*path.affinity
				},
				path.affinity,
				c_season,
				name.clone().unwrap()
			),
			..FetchIPv6Options::default()
		}).await;
		if fetch_current_leaderboard.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		let current_leaderboard = fetch_current_leaderboard.unwrap();
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		let players = current_leaderboard.data.players.unwrap_or(vec![]);
		let player = players
			.iter()
			.find(|&x| { x.game_name.to_lowercase() == name.clone().unwrap().to_lowercase() && x.tag_line.to_lowercase() == tag.clone().unwrap().to_lowercase() });
		if player.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player = player.unwrap();
		let response = LeaderboardV1DirectFetchResponse {
			status: 200,
			data: vec![player.to_owned()],
		};
		return json_response(&response, 200);
	} else if name.is_some() && tag.is_some() && season != c_season {
		let query =
			doc! {
            "affinity": path.affinity.clone(),
            "season": season,
            "player.gameName": name.clone().unwrap(),
            "player.tagLine": tag.clone().unwrap()
        };
		let get_from_db = get_db::<Document>(&client, "leaderboard_players", None)
			.find(query)
			.sort(doc! { "player.leaderboardRank": 1 })
			.limit(1000).await;
		if get_from_db.is_err() {
			return error_handler(vec![ErrorCodes::DatabaseError]);
		}
		let mut collected = vec![];
		let mut db = get_from_db.unwrap();
		while let Some(player) = db.try_next().await.unwrap() {
			let player: LeaderboardPlayerDB = from_document(player).unwrap();
			collected.push(player.player);
		}
		let response = LeaderboardV1Response {
			status: 200,
			data: collected,
		};
		return json_response(&response, 200);
	} else if puuid.is_some() && season == c_season {
		let validate_uuid = Uuid::parse_str(&puuid.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &puuid.clone().unwrap(), false, false).await;
		if account.is_err() {
			return error_handler(vec![account.err().unwrap()]);
		}
		let account = account.unwrap();
		rl.background_requests.fetch_add(account.background_requests, std::sync::atomic::Ordering::SeqCst);
		let account = account.data;
		let fetch_current_leaderboard = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!(
				"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}?size=50&query={}",
				if path.affinity == "br" || path.affinity == "latam" {
					"na"
				} else {
					&*path.affinity
				},
				path.affinity,
				c_season,
				account.name
			),
			..FetchIPv6Options::default()
		}).await;
		if fetch_current_leaderboard.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		let current_leaderboard = fetch_current_leaderboard.unwrap();
		let players = current_leaderboard.data.players.unwrap_or(vec![]);
		let player = players.iter().find(|&x| { x.game_name.to_lowercase() == account.name.to_lowercase() && x.tag_line.to_lowercase() == account.tag.to_lowercase() });
		if player.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player = player.unwrap();
		let response = LeaderboardV1DirectFetchResponse {
			status: 200,
			data: vec![player.to_owned()],
		};
		return json_response(&response, 200);
	} else if puuid.is_some() && season != c_season {
		let validate_uuid = Uuid::parse_str(&puuid.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let query = doc! {
            "affinity": path.affinity.clone(),
            "season": season,
            "player.puuid": puuid.clone().unwrap(),
        };
		let get_from_db = get_db::<Document>(&client, "leaderboard_players", None)
			.find(query)
			.sort(doc! { "player.leaderboardRank": 1 })
			.limit(1000).await;
		if get_from_db.is_err() {
			return error_handler(vec![ErrorCodes::DatabaseError]);
		}
		let mut collected = vec![];
		let mut db = get_from_db.unwrap();
		while let Some(player) = db.try_next().await.unwrap() {
			let player: LeaderboardPlayerDB = from_document(player).unwrap();
			collected.push(player.player);
		}
		let response = LeaderboardV1Response {
			status: 200,
			data: collected,
		};
		return json_response(&response, 200);
	}

	let query = doc! {
        "affinity": path.affinity.clone(),
        "season": season,
    };
	let get_from_db = get_db::<LeaderboardPlayerDB>(&client, "leaderboard_players", None)
		.find(query.clone())
		.sort(doc! { "player.leaderboardRank": 1 }).await;
	if get_from_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let collected_: Vec<LeaderboardPlayerDB> = get_from_db
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|_| vec![]);
	let collected = collected_
		.iter()
		.map(|x| x.player.clone())
		.collect::<Vec<_>>();

	let get_from_db_count = get_db::<Document>(&client, "leaderboard_players", None).count_documents(query.clone()).await;
	if get_from_db_count.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let count = get_from_db_count.unwrap();

	let tier_details = get_db::<LeaderboardMetadataDB>(&client, "leaderboard_metadata", None).find_one(query).await;
	if tier_details.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let tier_details = tier_details.unwrap();
	let tier_details = tier_details.unwrap();
	let thresholds = tier_details.thresholds;
	let immo_1 = thresholds.get("24").unwrap().ranked_rating_threshold;
	let immo_2 = thresholds.get("25").unwrap().ranked_rating_threshold;
	let immo_3 = thresholds.get("26").unwrap().ranked_rating_threshold;
	let radiant = thresholds.get("27").unwrap().ranked_rating_threshold;

	let update_time = tier_details.updated_at.timestamp_millis() / 1000;
	let response = LeaderboardV2Response {
		last_update: update_time,
		next_update: update_time + 1800,
		total_players: count as i32,
		radiant_threshold: radiant as i32,
		immortal_3_threshold: immo_3 as i32,
		immortal_2_threshold: immo_2 as i32,
		immortal_1_threshold: immo_1 as i32,
		players: collected,
	};
	json_response(&response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v3/leaderboard/{affinity}/{platform}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("platform" = String, Path, description = "Platform (pc, console)"),
		("season" = Option<String>, Query, description = "Season ID (optional)"),
		("size" = Option<i32>, Query, description = "Number of results per page (optional)"),
		("page" = Option<i32>, Query, description = "Page number (optional)"),
		("name" = Option<String>, Query, description = "Player name to search for (optional)"),
		("tag" = Option<String>, Query, description = "Player tag to search for (optional)")
	),
	responses(
		(status = 200, description = "Leaderboard retrieved successfully", body = LeaderboardV3Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Leaderboard not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn leaderboard_v3(
	Query(query): Query<LeaderboardV3Query>,
	Path(path): Path<LeaderboardV3Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let validate_platform = check_platform(&path.platform);
	if !validate_platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let mut db_query = doc! {};
	let c_season = get_c_season().await.to_string();

	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let size = query.size.clone().unwrap_or("1000".to_string()).parse::<i32>().unwrap_or(1000);
	let page = query.page.clone().unwrap_or("1".to_string()).parse::<i32>().unwrap_or(1);

	if query.season_short.is_some() && query.season_id.is_some() {
		return error_handler(vec![ErrorCodes::DuplicateSeasonQuery]);
	}
	let season = if let Some(q) = query.season_short.clone() {
		let seasons = get_short_ids().await;
		let search = seasons.iter().find(|&x| x.short_id == q);
		if search.is_none() {
			return error_handler(vec![ErrorCodes::InvalidSeason]);
		}
		let season = search.unwrap().season.clone();
		db_query.insert("season", season.as_str());
		season
	} else if let Some(q) = query.season_id.clone() {
		let season = get_season_by_id(&q).await;
		if season.is_none() {
			return error_handler(vec![ErrorCodes::InvalidSeason]);
		}
		let season = season.unwrap();
		db_query.insert("season", season.uuid.as_str());
		season.uuid
	} else {
		db_query.insert("season", c_season.as_str());
		c_season.clone()
	};

	let query_db = doc! {
        "affinity": path.affinity.clone(),
        "season": season.clone(),
    };
	let name = query.name.clone();
	let tag = query.tag.clone();
	let puuid = query.puuid.clone();
	if name.is_none() && tag.is_some() {
		return error_handler(vec![ErrorCodes::InvalidNameQuery]);
	}
	if tag.is_none() && name.is_some() {
		return error_handler(vec![ErrorCodes::InvalidTagQuery]);
	}

	let client = app_state.client.clone();
	let rl = extension.clone();

	let tier_details = get_db::<LeaderboardMetadataDB>(&client, "leaderboard_metadata", None).find_one(query_db.clone()).await;
	if tier_details.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let tier_details = tier_details.unwrap();
	if tier_details.is_none() {
		return error_handler(vec![ErrorCodes::LeaderboardMetadataNotFound]);
	}

	if name.is_some() && tag.is_some() && season == c_season {
		let fetch_current_leaderboard = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!(
				"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}?size=50&query={}",
				if path.affinity == "br" || path.affinity == "latam" {
					"na"
				} else {
					&*path.affinity
				},
				path.affinity,
				c_season,
				name.clone().unwrap()
			),
			..FetchIPv6Options::default()
		}).await;
		if fetch_current_leaderboard.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		let current_leaderboard = fetch_current_leaderboard.unwrap();
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		let players = current_leaderboard.data.players.unwrap_or(vec![]);
		let player = players
			.iter()
			.find(|&x| { x.game_name.to_lowercase() == name.clone().unwrap().to_lowercase() && x.tag_line.to_lowercase() == tag.clone().unwrap().to_lowercase() });
		if player.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player = player.unwrap().to_owned();
		let pagination = Pagination {
			total: 1,
			returned: 1,
			before: 0,
			after: 0,
		};
		let parsed = parse_v3_leaderboard_from_api(pagination, tier_details.unwrap(), vec![player]);
		return json_response(&parsed, 200);
	} else if name.is_some() && tag.is_some() && season != c_season {
		let query =
			doc! {
            "affinity": path.affinity.clone(),
            "season": season,
            "player.gameName": name.clone().unwrap(),
            "player.tagLine": tag.clone().unwrap()
        };
		let get_from_db = get_db::<Document>(&client, "leaderboard_players", None).find_one(query).await;
		if get_from_db.is_err() {
			return error_handler(vec![ErrorCodes::DatabaseError]);
		}
		let get_from_db = get_from_db.unwrap();
		if get_from_db.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player: LeaderboardPlayerDB = from_document(get_from_db.unwrap()).unwrap();
		let pagination = Pagination {
			total: 1,
			returned: 1,
			before: 0,
			after: 0,
		};
		let parsed = parse_v3_leaderboard(pagination, tier_details.unwrap(), vec![player]);
		return json_response(&parsed, 200);
	} else if puuid.is_some() && season == c_season {
		let validate_uuid = Uuid::parse_str(&puuid.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &puuid.clone().unwrap(), false, false).await;
		if account.is_err() {
			return error_handler(vec![account.err().unwrap()]);
		}
		let account = account.unwrap();
		rl.background_requests.fetch_add(account.background_requests, std::sync::atomic::Ordering::SeqCst);
		let account = account.data;
		let fetch_current_leaderboard = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!(
				"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}?size=50&query={}",
				if path.affinity == "br" || path.affinity == "latam" {
					"na"
				} else {
					&*path.affinity
				},
				path.affinity,
				c_season,
				account.name
			),
			..FetchIPv6Options::default()
		}).await;
		if fetch_current_leaderboard.is_err() {
			return error_handler(vec![ErrorCodes::FetchingResource]);
		}
		let current_leaderboard = fetch_current_leaderboard.unwrap();
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		let players = current_leaderboard.data.players.unwrap_or(vec![]);
		let player = players.iter().find(|&x| { x.game_name.to_lowercase() == account.name.to_lowercase() && x.tag_line.to_lowercase() == account.tag.to_lowercase() });
		if player.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player = player.unwrap().to_owned();
		let pagination = Pagination {
			total: 1,
			returned: 1,
			before: 0,
			after: 0,
		};
		let parsed = parse_v3_leaderboard_from_api(pagination, tier_details.unwrap(), vec![player]);
		return json_response(&parsed, 200);
	} else if puuid.is_some() && season != c_season {
		let validate_uuid = Uuid::parse_str(&puuid.clone().unwrap());
		if validate_uuid.is_err() {
			return error_handler(vec![ErrorCodes::InvalidUUID]);
		}
		let query = doc! {
            "affinity": path.affinity.clone(),
            "season": season,
            "player.puuid": puuid.clone().unwrap(),
        };
		let get_from_db = get_db::<Document>(&client, "leaderboard_players", None).find_one(query).await;
		if get_from_db.is_err() {
			return error_handler(vec![ErrorCodes::DatabaseError]);
		}
		let get_from_db = get_from_db.unwrap();
		if get_from_db.is_none() {
			return error_handler(vec![ErrorCodes::NotFoundInLeaderboard]);
		}
		let player: LeaderboardPlayerDB = from_document(get_from_db.unwrap()).unwrap();
		let pagination = Pagination {
			total: 1,
			returned: 1,
			before: 0,
			after: 0,
		};
		let parsed = parse_v3_leaderboard(pagination, tier_details.unwrap(), vec![player]);
		return json_response(&parsed, 200);
	}

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };
	let get_from_db = get_db::<LeaderboardPlayerDB>(&client, "leaderboard_players", None)
		.find(query_db.clone())
		.sort(doc! { "player.leaderboardRank": 1 })
		.skip(skip)
		.limit(size as i64).await;
	if get_from_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let collected_: Vec<LeaderboardPlayerDB> = get_from_db
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|_| vec![]);

	let get_from_db_count = get_db::<Document>(&client, "leaderboard_players", None).count_documents(query_db.clone()).await;
	if get_from_db_count.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let count = get_from_db_count.unwrap() as i32;

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };
	let pagination = Pagination {
		total: count,
		returned: collected_.len() as i32,
		before,
		after,
	};

	let parsed = parse_v3_leaderboard(pagination, tier_details.unwrap(), collected_);

	json_response(&parsed, 200)
}
