use std::sync::Arc;
use axum::body::Body;
use axum::extract::{ Path, State };
use axum::response::Response;
use crate::{
	error_handler,
	get_db,
	structs::{ database::Versions, paths::VersionPath, responses::{ VersionV1Data, VersionV1Response }, errors::SendError },
	ErrorCodes,
	VALORANT_REGIONS,
	check_affinity,
	AppState,
};
use mongodb::{ bson::doc };
use crate::structs::errors::json_response;

#[utoipa::path(
	get,
	path = "/valorant/v1/version/{affinity}",
	tag = "valorant",
	params(("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)")),
	responses(
		(status = 200, description = "Version data retrieved successfully", body = VersionV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Region not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
#[allow(non_snake_case)]
pub async fn Version(Path(path): Path<VersionPath>, State(app_state): State<Arc<AppState>>) -> Response {
	let client = app_state.client.clone();
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let path_region = path.affinity.to_lowercase();
	let region = if ["br", "latam"].iter().any(|x| x == &path_region.to_lowercase()) { String::from("na") } else { path_region.to_lowercase() };
	if !VALORANT_REGIONS.contains(&region.as_str()) {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let version_data_fetch = get_db::<Versions>(&client, "versions", None).find_one(doc! { "region": region }).await;
	if version_data_fetch.is_err() {
		return error_handler(vec![ErrorCodes::InternalError]);
	}
	let version_data = version_data_fetch.unwrap().unwrap();
	json_response(
		&(VersionV1Response {
			status: 200,
			data: VersionV1Data {
				region: version_data.region,
				branch: version_data.branch,
				build_date: version_data.build_date,
				build_ver: version_data.build_ver,
				last_checked: version_data.last_checked,
				version: version_data.version,
				version_for_api: version_data.version_for_api,
			},
		}),
		200
	)
}
