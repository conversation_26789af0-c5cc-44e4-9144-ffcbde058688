use crate::methods::utils::{ get_match_b2, RateLimiting };
use crate::structs::database::{ B2Match, LifetimeMMRHistory };
use crate::structs::errors::{ error_handler, ErrorCodes, SendError, json_response };
use crate::structs::helper::{
	fetch_matches_by_puuid,
	fetch_mmr_history_by_puuid,
	get_agent_by_id,
	get_gamepod_by_id,
	get_map_by_asset,
	get_map_by_name,
	get_queue_by_queue_id,
	get_queue_by_text,
	get_region_by_pod_id,
	get_short_season_by_id,
	get_valorant_account_by_id,
	get_valorant_account_by_name,
	query_string_builder,
	update_lifetime_mmr_history,
};
use crate::structs::parser::{ parse_mmr_history_stored, parse_mmr_history_v2_stored };
use crate::structs::paths::{
	StoredMMRHistoryByIDPath,
	StoredMMRHistoryByIDQuery,
	StoredMMRHistoryPath,
	StoredMMRHistoryQuery,
	StoredMMRHistoryV2ByIDPath,
	StoredMMRHistoryV2ByIDQuery,
	StoredMMRHistoryV2Path,
	StoredMMRHistoryV2Query,
	StoredMatchesByIDPath,
	StoredMatchesPath,
	StoredMatchesQuery,
};
use crate::structs::responses::{
	MMRHistoryV1Response,
	MMRHistoryV2Response,
	Pagination,
	StoredMMRResponse,
	StoredMMRV2Response,
	StoredMatch,
	StoredMatchMeta,
	StoredMatchMetaMap,
	StoredMatchMetaSeason,
	StoredMatchStats,
	StoredMatchStatsCharacter,
	StoredMatchStatsDamage,
	StoredMatchStatsShots,
	StoredMatchTeam,
	StoredMatchesResponse,
};
use crate::{ check_platform, get_db, AppState };
use axum::body::Body;
use axum::extract::{ Path, Query, State };
use axum::response::Response;
use axum::Extension;
use futures::future::join_all;
use futures::TryStreamExt;
use mongodb::bson::{ doc, DateTime };
use serde_json::json;
use std::collections::HashMap;
use std::sync::atomic::Ordering;
use std::sync::Arc;

#[utoipa::path(
	get,
	path = "/valorant/v1/lifetime/matches/{affinity}/{name}/{tag}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag"),
		("mode" = Option<String>, Query, description = "Game mode filter (optional)"),
		("map" = Option<String>, Query, description = "Map filter (optional)"),
		("size" = Option<i32>, Query, description = "Number of results (optional)")
	),
	responses(
		(status = 200, description = "Stored match history retrieved successfully", body = StoredMatchesResponse),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn stored_matches(
	Query(query): Query<StoredMatchesQuery>,
	Path(path): Path<StoredMatchesPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let mut queries: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {};
	if query.mode.is_some() {
		let mode = get_queue_by_text(&query.mode.clone().unwrap()).await;
		if mode.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMode]);
		}
		let mode = mode.unwrap();
		queries.insert("queue".to_string(), mode.queueId.clone());
		db_query.insert("meta.q", mode.queueId);
	}
	if query.map.is_some() {
		let map = get_map_by_name(&query.map.clone().unwrap()).await;
		if map.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMap]);
		}
		let map = map.unwrap();
		db_query.insert("meta.map_id", map.mapUrl);
	}
	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let query_string = query_string_builder(queries);
	let size = query.size.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(0);
	let page = query.page.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(1);

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, Ordering::SeqCst);
	let account = account_wr.data;
	db_query.insert("players.id", &account.puuid);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let history = fetch_matches_by_puuid(conn, &account.puuid, &region, &query_string, "pc").await;
	if history.is_err() {
		return error_handler(vec![history.unwrap_err()]);
	}
	let history = history.unwrap();
	if !history.is_from_redis {
		rl.background_requests.fetch_add(1, Ordering::SeqCst);
	}
	let mut matches = history.data.matches.clone();
	let mut fetch = vec![];
	for match_ in matches.iter_mut() {
		fetch.push(get_match_b2(&client, app_state.redis.clone(), match_.match_id.to_string(), region.clone()));
	}
	let _ = join_all(fetch).await;

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };
	let matches_db = get_db::<B2Match>(&client, "match_metadata_b2", None)
		.find(db_query.clone())
		.sort(doc! { "meta.s_ms": -1 })
		.limit(size as i64)
		.skip(skip).await;
	if matches_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let matches_db = matches_db.unwrap();
	let matches_db = matches_db.try_collect().await.unwrap_or_else(|_| vec![]);
	let count = get_db::<B2Match>(&client, "match_metadata_b2", None).count_documents(db_query).await.unwrap_or(0) as i32;

	let mut parsed_matches: Vec<StoredMatch> = vec![];
	for x in matches_db {
		let user = x.players
			.iter()
			.find(|x| x.id.to_string() == account.puuid)
			.unwrap();
		let map = get_map_by_asset(&x.meta.map_id).await;
		let mode = get_queue_by_queue_id(&x.meta.q).await;
		let season = get_short_season_by_id(&x.meta.s_id).await;
		let character = get_agent_by_id(&user.c).await;
		let teams = x.teams.clone();
		let teams_type_ = teams.first().unwrap().clone().id;
		let teams_type = if teams_type_ == "Red" || teams_type_ == "Blue" { "TEAMS" } else { "UUID" };

		parsed_matches.push(StoredMatch {
			meta: StoredMatchMeta {
				id: x.m_id,
				map: StoredMatchMetaMap {
					id: if let Some(map) = map.clone() {
						map.uuid.to_string()
					} else {
						"unknown".to_string()
					},
					name: if let Some(map) = map {
						map.displayName.to_string()
					} else {
						"unknown".to_string()
					},
				},
				version: x.meta.v,
				mode: if let Some(mode) = mode {
					mode.displayName.unwrap()
				} else {
					"unknown".to_string()
				},
				started_at: DateTime::from_millis(x.meta.s_ms as i64)
					.try_to_rfc3339_string()
					.unwrap(),
				season: StoredMatchMetaSeason {
					id: if let Some(season) = season.clone() {
						season.season.to_string()
					} else {
						"unknown".to_string()
					},
					short: if let Some(season) = season {
						season.short_id.to_string()
					} else {
						"unknown".to_string()
					},
				},
				region: get_region_by_pod_id(&x.meta.pod_id),
				cluster: get_gamepod_by_id(&x.meta.pod_id).await,
			},
			stats: StoredMatchStats {
				puuid: user.p.to_string(),
				team: user.t.to_string(),
				level: user.l,
				character: StoredMatchStatsCharacter {
					id: character.clone().unwrap().uuid.to_string(),
					name: character.unwrap().displayName.to_string(),
				},
				tier: user.tier as u32,
				score: user.s,
				kills: user.k,
				deaths: user.d,
				assists: user.a,
				shots: StoredMatchStatsShots {
					head: user.head,
					body: user.body,
					leg: user.leg,
				},
				damage: StoredMatchStatsDamage {
					made: user.d_d,
					received: user.d_r,
				},
			},
			teams: StoredMatchTeam {
				red: if teams_type == "TEAMS" {
					teams
						.iter()
						.find(|x| x.id == "Red")
						.unwrap().p
				} else {
					teams
						.iter()
						.find(|x| x.id == user.id)
						.unwrap().p
				},
				blue: if teams_type == "TEAMS" {
					teams
						.iter()
						.find(|x| x.id == "Blue")
						.unwrap().p
				} else {
					teams
						.iter()
						.find(|x| x.id == user.id)
						.unwrap().p
				},
			},
		});
	}

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };
	json_response(
		&(StoredMatchesResponse {
			status: 200,
			results: Pagination {
				total: count,
				returned: parsed_matches.len() as i32,
				before,
				after,
			},
			data: parsed_matches,
		}),
		200
	)
}

#[utoipa::path(
	get,
	path = "/valorant/v1/by-puuid/lifetime/matches/{affinity}/{puuid}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("puuid" = String, Path, description = "Player UUID"),
		("mode" = Option<String>, Query, description = "Game mode filter (optional)"),
		("map" = Option<String>, Query, description = "Map filter (optional)"),
		("size" = Option<i32>, Query, description = "Number of results (optional)")
	),
	responses(
		(status = 200, description = "Stored match history retrieved successfully", body = StoredMatchesResponse),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn stored_matches_by_id(
	Query(query): Query<StoredMatchesQuery>,
	Path(path): Path<StoredMatchesByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let mut queries: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {};
	if query.mode.is_some() {
		let mode = get_queue_by_text(&query.mode.clone().unwrap()).await;
		if mode.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMode]);
		}
		let mode = mode.unwrap();
		queries.insert("queue".to_string(), mode.queueId.clone());
		db_query.insert("meta.q", mode.queueId);
	}
	if query.map.is_some() {
		let map = get_map_by_name(&query.map.clone().unwrap()).await;
		if map.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMap]);
		}
		let map = map.unwrap();
		db_query.insert("meta.map_id", map.mapUrl);
	}
	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let query_string = query_string_builder(queries);
	let size = query.size.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(0);
	let page = query.page.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(1);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, Ordering::SeqCst);
	let account = account_wr.data;
	db_query.insert("players.id", &account.puuid);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let history = fetch_matches_by_puuid(conn, &account.puuid, &region, &query_string, "pc").await;
	if history.is_err() {
		return error_handler(vec![history.unwrap_err()]);
	}
	let history = history.unwrap();
	if !history.is_from_redis {
		rl.background_requests.fetch_add(1, Ordering::SeqCst);
	}
	let mut matches = history.data.matches.clone();
	let mut fetch = vec![];
	for match_ in matches.iter_mut() {
		fetch.push(get_match_b2(&client, app_state.redis.clone(), match_.match_id.to_string(), region.clone()));
	}
	let _ = join_all(fetch).await;

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };

	let matches_db = get_db::<B2Match>(&client, "match_metadata_b2", None)
		.find(db_query.clone())
		.sort(doc! { "meta.s_ms": -1 })
		.limit(size as i64)
		.skip(skip).await;
	if matches_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let matches_db = matches_db.unwrap();
	let matches_db = matches_db.try_collect().await.unwrap_or_else(|_| vec![]);
	let count = get_db::<B2Match>(&client, "match_metadata_b2", None).count_documents(db_query).await.unwrap_or(0) as i32;
	let mut parsed_matches: Vec<StoredMatch> = vec![];
	for x in matches_db {
		let user = x.players
			.iter()
			.find(|x| x.id.to_string() == account.puuid)
			.unwrap();
		let map = get_map_by_asset(&x.meta.map_id).await;
		let mode = get_queue_by_queue_id(&x.meta.q).await;
		let season = get_short_season_by_id(&x.meta.s_id).await;
		let character = get_agent_by_id(&user.c).await;
		let teams = x.teams.clone();
		let teams_type_ = teams.first().unwrap().clone().id;
		let teams_type = if teams_type_ == "Red" || teams_type_ == "Blue" { "TEAMS" } else { "UUID" };

		parsed_matches.push(StoredMatch {
			meta: StoredMatchMeta {
				id: x.m_id,
				map: StoredMatchMetaMap {
					id: if let Some(map) = map.clone() {
						map.uuid.to_string()
					} else {
						"unknown".to_string()
					},
					name: if let Some(map) = map {
						map.displayName.to_string()
					} else {
						"unknown".to_string()
					},
				},
				version: x.meta.v,
				mode: if let Some(mode) = mode {
					mode.displayName.unwrap()
				} else {
					"unknown".to_string()
				},
				started_at: DateTime::from_millis(x.meta.s_ms as i64)
					.try_to_rfc3339_string()
					.unwrap(),
				season: StoredMatchMetaSeason {
					id: if let Some(season) = season.clone() {
						season.season.to_string()
					} else {
						"unknown".to_string()
					},
					short: if let Some(season) = season {
						season.short_id.to_string()
					} else {
						"unknown".to_string()
					},
				},
				region: get_region_by_pod_id(&x.meta.pod_id),
				cluster: get_gamepod_by_id(&x.meta.pod_id).await,
			},
			stats: StoredMatchStats {
				puuid: user.p.to_string(),
				team: user.t.to_string(),
				level: user.l,
				character: StoredMatchStatsCharacter {
					id: character.clone().unwrap().uuid.to_string(),
					name: character.unwrap().displayName.to_string(),
				},
				tier: user.tier as u32,
				score: user.s,
				kills: user.k,
				deaths: user.d,
				assists: user.a,
				shots: StoredMatchStatsShots {
					head: user.head,
					body: user.body,
					leg: user.leg,
				},
				damage: StoredMatchStatsDamage {
					made: user.d_d,
					received: user.d_r,
				},
			},
			teams: StoredMatchTeam {
				red: if teams_type == "TEAMS" {
					teams
						.iter()
						.find(|x| x.id == "Red")
						.unwrap().p
				} else {
					teams
						.iter()
						.find(|x| x.id == user.id)
						.unwrap().p
				},
				blue: if teams_type == "TEAMS" {
					teams
						.iter()
						.find(|x| x.id == "Blue")
						.unwrap().p
				} else {
					teams
						.iter()
						.find(|x| x.id == user.id)
						.unwrap().p
				},
			},
		});
	}

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };
	json_response(
		&(StoredMatchesResponse {
			status: 200,
			results: Pagination {
				total: count,
				returned: parsed_matches.len() as i32,
				before,
				after,
			},
			data: parsed_matches,
		}),
		200
	)
}

#[utoipa::path(
	get,
	path = "/valorant/v1/lifetime/mmr-history/{affinity}/{name}/{tag}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag"),
		("size" = Option<i32>, Query, description = "Number of results (optional)")
	),
	responses(
		(status = 200, description = "Stored MMR history retrieved successfully", body = StoredMMRResponse),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn stored_mmr_history(
	Query(query): Query<StoredMMRHistoryQuery>,
	Path(path): Path<StoredMMRHistoryPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let platform = "pc";
	let queries: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {};
	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let size = query.size.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(0);
	let page = query.page.clone().unwrap_or("1".to_string()).parse::<i32>().unwrap_or(1);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, Ordering::SeqCst);
	let account = account_wr.data;
	db_query.insert("p_id", &account.puuid);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &account.region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), platform).await;
	}

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };
	let mmr_db = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None)
		.find(db_query.clone())
		.sort(doc! { "d": -1 })
		.limit(size as i64)
		.skip(skip).await;
	if mmr_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mmr_db = mmr_db
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|_| vec![]);

	let count = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None).count_documents(db_query).await.unwrap_or(0) as i32;
	let parsed_entries = parse_mmr_history_stored(mmr_db).await;

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };
	json_response(
		&(StoredMMRResponse {
			status: 200,
			results: Pagination {
				total: count,
				returned: parsed_entries.len() as i32,
				before,
				after,
			},
			data: parsed_entries,
		}),
		200
	)
}

#[utoipa::path(
	get,
	path = "/valorant/v1/by-puuid/lifetime/mmr-history/{affinity}/{puuid}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("puuid" = String, Path, description = "Player UUID"),
		("size" = Option<i32>, Query, description = "Number of results (optional)")
	),
	responses(
		(status = 200, description = "Stored MMR history retrieved successfully", body = StoredMMRResponse),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn stored_mmr_history_by_id(
	Query(query): Query<StoredMMRHistoryByIDQuery>,
	Path(path): Path<StoredMMRHistoryByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let platform = "pc";
	let queries: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {};
	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let size = query.size.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(0);
	let page = query.page.clone().unwrap_or("1".to_string()).parse::<i32>().unwrap_or(1);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account = account.unwrap();
	rl.background_requests.fetch_add(account.background_requests, Ordering::SeqCst);
	let account = account.data;
	db_query.insert("p_id", &account.puuid);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &account.region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), platform).await;
	}

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };
	let mmr_db = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None)
		.find(db_query.clone())
		.sort(doc! { "d": -1 })
		.limit(size as i64)
		.skip(skip).await;
	if mmr_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mmr_db = mmr_db
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|_| vec![]);

	let count = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None).count_documents(db_query).await.unwrap_or(0) as i32;
	let parsed_entries = parse_mmr_history_stored(mmr_db).await;

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };

	json_response(
		&(StoredMMRResponse {
			status: 200,
			results: Pagination {
				total: count,
				returned: parsed_entries.len() as i32,
				before,
				after,
			},
			data: parsed_entries,
		}),
		200
	)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/stored-mmr-history/{affinity}/{platform}/{name}/{tag}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("platform" = String, Path, description = "Platform (pc, console)"),
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag"),
		("size" = Option<i32>, Query, description = "Number of results (optional)")
	),
	responses(
		(status = 200, description = "Stored MMR history retrieved successfully", body = StoredMMRV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn stored_mmr_history_v2(
	Query(query): Query<StoredMMRHistoryV2Query>,
	Path(path): Path<StoredMMRHistoryV2Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let platform = check_platform(&path.platform);
	if !platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let platform = path.platform.to_lowercase();

	let queries: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {
        "p": &platform
    };
	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let size = query.size.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(0);
	let page = query.page.clone().unwrap_or("1".to_string()).parse::<i32>().unwrap_or(1);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, Ordering::SeqCst);
	let account = account_wr.data;
	db_query.insert("p_id", &account.puuid);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, &platform, &account.region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), &platform).await;
	}

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };
	let mmr_db = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None)
		.find(db_query.clone())
		.sort(doc! { "d": -1 })
		.limit(size as i64)
		.skip(skip).await;
	if mmr_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mmr_db = mmr_db
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|_| vec![]);

	let count = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None).count_documents(db_query).await.unwrap_or(0) as i32;
	let parsed_entries = parse_mmr_history_v2_stored(mmr_db).await;

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };

	json_response(
		&(StoredMMRV2Response {
			status: 200,
			results: Pagination {
				total: count,
				returned: parsed_entries.len() as i32,
				before,
				after,
			},
			data: parsed_entries,
		}),
		200
	)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/by-puuid/stored-mmr-history/{affinity}/{platform}/{puuid}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("platform" = String, Path, description = "Platform (pc, console)"),
		("puuid" = String, Path, description = "Player UUID"),
		("size" = Option<i32>, Query, description = "Number of results (optional)")
	),
	responses(
		(status = 200, description = "Stored MMR history retrieved successfully", body = StoredMMRV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn stored_mmr_history_v2_by_id(
	Query(query): Query<StoredMMRHistoryV2ByIDQuery>,
	Path(path): Path<StoredMMRHistoryV2ByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let platform = check_platform(&path.platform);
	if !platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let platform = path.platform.to_lowercase();

	let queries: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {
        "p": &platform
    };
	if query.size.is_none() && query.page.is_some() {
		return error_handler(vec![ErrorCodes::MissingSizeQuery]);
	}
	if query.size.is_some() && query.page.is_some() {
		let parsed_size = query.size.clone().unwrap().parse::<i32>();
		let parsed_page = query.page.clone().unwrap().parse::<i32>();
		if parsed_size.is_err() || parsed_page.is_err() {
			return error_handler(vec![ErrorCodes::InvalidSizeOrPageQuery]);
		}
		let size = parsed_size.unwrap();
		let page = parsed_page.unwrap();
		if size < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfSize]);
		}
		if page < 1 {
			return error_handler(vec![ErrorCodes::InvalidSizeOfPage]);
		}
	}
	let size = query.size.clone().unwrap_or("0".to_string()).parse::<i32>().unwrap_or(0);
	let page = query.page.clone().unwrap_or("1".to_string()).parse::<i32>().unwrap_or(1);

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account = account.unwrap();
	rl.background_requests.fetch_add(account.background_requests, Ordering::SeqCst);
	let account = account.data;
	db_query.insert("p_id", &account.puuid);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, &platform, &account.region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), &platform).await;
	}

	let skip = if page * size - size < 0 { 0 } else { (page * size - size) as u64 };
	let mmr_db = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None)
		.find(db_query.clone())
		.sort(doc! { "d": -1 })
		.limit(size as i64)
		.skip(skip).await;
	if mmr_db.is_err() {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mmr_db = mmr_db
		.unwrap()
		.try_collect().await
		.unwrap_or_else(|_| vec![]);

	let count = get_db::<LifetimeMMRHistory>(&client, "mmr_history", None).count_documents(db_query).await.unwrap_or(0) as i32;
	let parsed_entries = parse_mmr_history_v2_stored(mmr_db).await;

	let before = if page * size - size > count { count } else { page * size - size };
	let after = if count - page * size < 0 { 0 } else { count - page * size };
	json_response(
		&(StoredMMRV2Response {
			status: 200,
			results: Pagination {
				total: count,
				returned: parsed_entries.len() as i32,
				before,
				after,
			},
			data: parsed_entries,
		}),
		200
	)
}
