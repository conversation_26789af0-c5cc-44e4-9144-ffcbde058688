use crate::methods::utils::RateLimiting;
use crate::structs::errors::{ error_handler, json_response, ErrorCodes, SendError };
use crate::structs::helper::{ fetch_mmr_by_puuid, fetch_mmr_history_by_puuid, get_valorant_account_by_id, get_valorant_account_by_name };
use crate::structs::parser::{ parse_mmr_v1, parse_mmr_v2, parse_mmr_v3 };
use crate::structs::paths::{ MMRV1ByIDPath, MMRV1Path, MMRV3ByIDPath, MMRV3Path };
use crate::structs::responses::{ MMRV1Response, MMRV2Response, MMRV3Response };
use crate::{ check_affinity, check_platform, AppState };
use axum::body::Body;
use axum::extract::{ Path, State };
use axum::response::Response;
use axum::Extension;
use std::sync::Arc;
use uuid::Uuid;
use utoipa::path;

#[utoipa::path(
	get,
	path = "/valorant/v1/by-puuid/mmr/{affinity}/{puuid}",
	tag = "valorant",
	params(("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"), ("puuid" = String, Path, description = "Player UUID")),
	responses(
		(status = 200, description = "MMR data retrieved successfully", body = MMRV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_mmr_v1_by_id(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Path(path): Path<MMRV1ByIDPath>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wrapper.data;
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if mmr_history_data.data.matches.is_empty() {
		return error_handler(vec![ErrorCodes::MMRNoDataAvailable]);
	}
	let formatted_response = parse_mmr_v1(mmr_history_data.data, &account).await;
	rl.redis_cache_ttl.store(mmr_history_data.ttl as isize, std::sync::atomic::Ordering::SeqCst);
	json_response(&formatted_response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v1/mmr/{affinity}/{name}/{tag}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag")
	),
	responses(
		(status = 200, description = "MMR data retrieved successfully", body = MMRV1Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_mmr_v1_by_name(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Path(path): Path<MMRV1Path>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wrapper.data;
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	if mmr_history_data.data.matches.is_empty() {
		return error_handler(vec![ErrorCodes::MMRNoDataAvailable]);
	}
	let formatted_response = parse_mmr_v1(mmr_history_data.data, &account).await;
	rl.redis_cache_ttl.store(mmr_history_data.ttl as isize, std::sync::atomic::Ordering::SeqCst);
	json_response(&formatted_response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/by-puuid/mmr/{affinity}/{puuid}",
	tag = "valorant",
	params(("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"), ("puuid" = String, Path, description = "Player UUID")),
	responses(
		(status = 200, description = "MMR data retrieved successfully", body = MMRV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_mmr_v2_by_id(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Path(path): Path<MMRV1ByIDPath>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wrapper.data;

	let mut ttls: Vec<i16> = vec![];
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	ttls.push(mmr_history_data.ttl);
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	if mmr_history_data.data.matches.is_empty() {
		return error_handler(vec![ErrorCodes::MMRNoDataAvailable]);
	}
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_player_data = fetch_mmr_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_player_data.is_err() {
		return error_handler(vec![mmr_player_data.err().unwrap()]);
	}
	let mmr_player_data = mmr_player_data.unwrap();
	ttls.push(mmr_player_data.ttl);
	if !mmr_player_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	let formatted_response = parse_mmr_v2(mmr_history_data.data, mmr_player_data.data, &account).await;
	let min_ttl = ttls.iter().min().unwrap();
	rl.redis_cache_ttl.store(*min_ttl as isize, std::sync::atomic::Ordering::SeqCst);
	json_response(&formatted_response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v2/mmr/{affinity}/{name}/{tag}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag")
	),
	responses(
		(status = 200, description = "MMR data retrieved successfully", body = MMRV2Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_mmr_v2_by_name(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Path(path): Path<MMRV1Path>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wrapper.data;

	let mut ttls: Vec<i16> = vec![];
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn.clone(), &account.puuid, "pc", &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	ttls.push(mmr_history_data.ttl);
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	if mmr_history_data.data.matches.is_empty() {
		return error_handler(vec![ErrorCodes::MMRNoDataAvailable]);
	}
	let mmr_player_data = fetch_mmr_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_player_data.is_err() {
		return error_handler(vec![mmr_player_data.err().unwrap()]);
	}
	let mmr_player_data = mmr_player_data.unwrap();
	ttls.push(mmr_player_data.ttl);
	if !mmr_player_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	let formatted_response = parse_mmr_v2(mmr_history_data.data, mmr_player_data.data, &account).await;
	let min_ttl = ttls.iter().min().unwrap();
	rl.redis_cache_ttl.store(*min_ttl as isize, std::sync::atomic::Ordering::SeqCst);
	json_response(&formatted_response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v3/mmr/{affinity}/{platform}/{name}/{tag}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("platform" = String, Path, description = "Platform (pc, console)"),
		("name" = String, Path, description = "Riot ID name"),
		("tag" = String, Path, description = "Riot ID tag")
	),
	responses(
		(status = 200, description = "MMR data retrieved successfully", body = MMRV3Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_mmr_v3_by_name(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Path(path): Path<MMRV3Path>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_platform = check_platform(&path.platform);
	if !validate_platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wrapper.data;

	let mut ttls: Vec<i16> = vec![];
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn.clone(), &account.puuid, &path.platform, &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	ttls.push(mmr_history_data.ttl);
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	if mmr_history_data.data.matches.is_empty() {
		return error_handler(vec![ErrorCodes::MMRNoDataAvailable]);
	}
	let mmr_player_data = fetch_mmr_by_puuid(conn, &account.puuid, &path.platform, &region).await;
	if mmr_player_data.is_err() {
		return error_handler(vec![mmr_player_data.err().unwrap()]);
	}
	let mmr_player_data = mmr_player_data.unwrap();
	ttls.push(mmr_player_data.ttl);
	if !mmr_player_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	let formatted_response = parse_mmr_v3(mmr_history_data.data, mmr_player_data.data, &account, &client, &path.affinity, &path.platform).await;
	let min_ttl = ttls.iter().min().unwrap();
	rl.redis_cache_ttl.store(*min_ttl as isize, std::sync::atomic::Ordering::SeqCst);
	json_response(&formatted_response, 200)
}

#[utoipa::path(
	get,
	path = "/valorant/v3/by-puuid/mmr/{affinity}/{platform}/{puuid}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)"),
		("platform" = String, Path, description = "Platform (pc, console)"),
		("puuid" = String, Path, description = "Player UUID")
	),
	responses(
		(status = 200, description = "MMR data retrieved successfully", body = MMRV3Response),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Account not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
pub async fn get_mmr_v3_by_id(State(app_state): State<Arc<AppState>>, Extension(extension): Extension<Arc<RateLimiting>>, Path(path): Path<MMRV3ByIDPath>) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_platform = check_platform(&path.platform);
	if !validate_platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, redis, &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wrapper = account.unwrap();
	rl.background_requests.fetch_add(account_wrapper.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wrapper.data;

	let mut ttls: Vec<i16> = vec![];
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn.clone(), &account.puuid, &path.platform, &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	ttls.push(mmr_history_data.ttl);
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	if mmr_history_data.data.matches.is_empty() {
		return error_handler(vec![ErrorCodes::MMRNoDataAvailable]);
	}
	let mmr_player_data = fetch_mmr_by_puuid(conn, &account.puuid, &path.platform, &region).await;
	if mmr_player_data.is_err() {
		return error_handler(vec![mmr_player_data.err().unwrap()]);
	}
	let mmr_player_data = mmr_player_data.unwrap();
	ttls.push(mmr_player_data.ttl);
	if !mmr_player_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
	}
	let formatted_response = parse_mmr_v3(mmr_history_data.data, mmr_player_data.data, &account, &client, &path.affinity, &path.platform).await;
	let min_ttl = ttls.iter().min().unwrap();
	rl.redis_cache_ttl.store(*min_ttl as isize, std::sync::atomic::Ordering::SeqCst);
	json_response(&formatted_response, 200)
}
