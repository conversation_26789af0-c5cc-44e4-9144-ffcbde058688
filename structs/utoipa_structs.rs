use std::collections::HashMap;
use serde::{ Deserialize, Serialize };
use utoipa::ToSchema;
use uuid::Uuid;

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone, ToSchema)]
#[serde(rename_all = "PascalCase")]
pub struct StoreFrontV2 {
	pub featured_bundle: FeaturedBundle,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone, ToSchema)]
#[serde(rename_all = "PascalCase")]
pub struct FeaturedBundle {
	pub bundle: Bundle,
	pub bundles: Vec<Bundle>,
	pub bundle_remaining_duration_in_seconds: u32,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone, ToSchema)]
#[serde(rename_all = "PascalCase")]
pub struct Bundle {
	#[serde(rename = "ID")]
	pub id: String,
	#[serde(rename = "DataAssetID")]
	pub data_asset_id: String,
	#[serde(rename = "CurrencyID")]
	pub currency_id: String,
	pub items: Vec<BundleItem>,
	pub total_discount_percent: f32,
	pub duration_remaining_in_seconds: u32,
	pub wholesale_only: bool,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone, ToSchema)]
#[serde(rename_all = "PascalCase")]
pub struct BundleItem {
	pub item: Item,
	pub base_price: i32,
	#[serde(rename = "CurrencyID")]
	pub currency_id: String,
	pub discount_percent: f32,
	pub discounted_price: f32,
	pub is_promo_item: bool,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone, ToSchema)]
#[serde(rename_all = "PascalCase")]
pub struct Item {
	#[serde(rename = "ItemTypeID")]
	pub item_type_id: String,
	#[serde(rename = "ItemID")]
	pub item_id: String,
	pub amount: u32,
}

#[derive(Serialize, Deserialize, Debug, Clone, Eq, PartialEq, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ContentV1 {
	pub version: String,
	pub characters: Vec<ContentItem>,
	pub maps: Vec<ContentItem>,
	pub chromas: Vec<ContentItem>,
	pub skins: Vec<ContentItem>,
	pub skin_levels: Vec<ContentItem>,
	pub equips: Vec<ContentItem>,
	pub game_modes: Vec<ContentItem>,
	pub sprays: Vec<ContentItem>,
	pub spray_levels: Vec<ContentItem>,
	pub charms: Vec<ContentItem>,
	pub charm_levels: Vec<ContentItem>,
	pub player_cards: Vec<ContentItem>,
	pub player_titles: Vec<ContentItem>,
	pub acts: Vec<ContentItem>,
	pub ceremonies: Vec<ContentItem>,
}

#[derive(Serialize, Deserialize, Debug, Clone, Eq, PartialEq, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ContentItem {
	pub id: Option<String>,
	pub name: String,
	pub asset_name: String,
	pub localized_names: Option<HashMap<String, String>>,
}

#[derive(Serialize, Deserialize, Debug, Clone, Eq, PartialEq, ToSchema)]
#[serde(rename_all = "lowercase")]
pub enum ActType {
	Act,
	Episode,
}

#[derive(Serialize, Deserialize, Debug, Clone, Eq, PartialEq, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ContentAct {
	pub id: Option<String>,
	pub parent_id: Option<String>,
	pub r#type: ActType,
	pub name: String,
	pub localized_names: Option<HashMap<String, String>>,
	pub is_active: bool,
}
