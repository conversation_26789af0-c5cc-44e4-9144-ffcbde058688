use crate::get_db;
use crate::structs::database::{ Account, LeaderboardMetadataDB, LeaderboardPlayerDB, LifetimeMMRHistory, PremierTeamDB, PremierTeamGamesLeagueString };
use crate::structs::enums::RiotPlatforms;
use crate::structs::helper::{
	calculate_elo,
	format_old_api_date,
	get_agent_by_id,
	get_agents,
	get_c_season,
	get_gamemode_by_partial_asset,
	get_gamepod_by_id,
	get_gears,
	get_map_by_asset,
	get_map_by_id,
	get_old_tier,
	get_queue_by_queue_id,
	get_short_ids,
	get_short_season_by_id,
	get_tier,
	get_tier_protection_status,
	get_weapons,
	is_old,
	premier_extract_hex_color,
};
use crate::structs::pvp_api::PVPMMRPlayerV1;
use crate::structs::responses::{
	AgentIdNameCombo,
	LeaderboardV3Data,
	LeaderboardV3DataPlayer,
	LeaderboardV3DataThreshold,
	LeaderboardV3DataThresholdTier,
	LeaderboardV3Response,
	MMRDataImages,
	MMRHistoryV1Data,
	MMRHistoryV1DataMap,
	MMRHistoryV1Response,
	MMRHistoryV2Data,
	MMRHistoryV2History,
	MMRHistoryV2Response,
	MMRV1Data,
	MMRV1Response,
	MMRV2CurrentData,
	MMRV2Data,
	MMRV2HighestRank,
	MMRV2Response,
	MMRV3Account,
	MMRV3Current,
	MMRV3Data,
	MMRV3LeaderboardPlacement,
	MMRV3Peak,
	MMRV3Response,
	MMRV3Seasonal,
	MapIdNameCombo,
	MatchesV2Data,
	MatchesV2DataCoach,
	MatchesV2DataKill,
	MatchesV2DataMetadata,
	MatchesV2DataMetadataPremierInfo,
	MatchesV2DataObserver,
	MatchesV2DataPlatform,
	MatchesV2DataPlatformOs,
	MatchesV2DataPlayer,
	MatchesV2DataPlayerAbilityCasts,
	MatchesV2DataPlayerAssets,
	MatchesV2DataPlayerAssetsAgent,
	MatchesV2DataPlayerAssetsCard,
	MatchesV2DataPlayerBehavior,
	MatchesV2DataPlayerBehaviorFriendlyFire,
	MatchesV2DataPlayerEconomy,
	MatchesV2DataPlayerEconomyValue,
	MatchesV2DataPlayerSessionPlaytime,
	MatchesV2DataPlayerStats,
	MatchesV2DataPlayers,
	MatchesV2DataRound,
	MatchesV2DataRoundDefuseEvents,
	MatchesV2DataRoundEventLocation,
	MatchesV2DataRoundPlantEvents,
	MatchesV2DataRoundPlayer,
	MatchesV2DataRoundPlayerLocationsOnEvent,
	MatchesV2DataRoundPlayerStats,
	MatchesV2DataRoundPlayerStatsAbilityCasts,
	MatchesV2DataRoundPlayerStatsDamageEvents,
	MatchesV2DataRoundPlayerStatsEconomy,
	MatchesV2DataRoundPlayerStatsEconomyEquipmentArmor,
	MatchesV2DataRoundPlayerStatsEconomyEquipmentAssets,
	MatchesV2DataRoundPlayerStatsEconomyEquipmentAssetsArmor,
	MatchesV2DataRoundPlayerStatsEconomyEquipmentWeapon,
	MatchesV2DataRoundPlayerStatsKillEvents,
	MatchesV2DataRoundPlayerStatsKillEventsAssets,
	MatchesV2DataRoundPlayerStatsKillEventsAssistants,
	MatchesV2DataTeam,
	MatchesV2DataTeamRoster,
	MatchesV2DataTeamRosterCustomization,
	MatchesV2DataTeams,
	MatchesV4Data,
	MatchesV4DataCoach,
	MatchesV4DataKill,
	MatchesV4DataMetadata,
	MatchesV4DataMetadataPartyRRPenalty,
	MatchesV4DataMetadataQueue,
	MatchesV4DataObserver,
	MatchesV4DataPlayer,
	MatchesV4DataPlayerAbilityCasts,
	MatchesV4DataPlayerBehavior,
	MatchesV4DataPlayerBehaviorFriendlyFire,
	MatchesV4DataPlayerCustomization,
	MatchesV4DataPlayerEconomy,
	MatchesV4DataPlayerEconomyLoadoutValue,
	MatchesV4DataPlayerEconomySpent,
	MatchesV4DataPlayerStats,
	MatchesV4DataPlayerStatsDamage,
	MatchesV4DataRound,
	MatchesV4DataRoundDefuse,
	MatchesV4DataRoundLocation,
	MatchesV4DataRoundPlant,
	MatchesV4DataRoundPlayer,
	MatchesV4DataRoundPlayerLocations,
	MatchesV4DataRoundPlayerStats,
	MatchesV4DataRoundPlayerStatsAbilityCasts,
	MatchesV4DataRoundPlayerStatsDamageEvents,
	MatchesV4DataRoundPlayerStatsEconomy,
	MatchesV4DataRoundPlayerStatsEconomyArmor,
	MatchesV4DataRoundPlayerStatsEconomyWeapon,
	MatchesV4DataRoundPlayerStatsStats,
	MatchesV4DataTeam,
	MatchesV4DataTeamPremierRoster,
	MatchesV4DataTeamPremierRosterCustomization,
	MatchesV4DataTeamRounds,
	Pagination,
	PremierTeamHistoryV1Response,
	PremierTeamHistoryV1ResponseData,
	PremierTeamLiteResponseData,
	PremierTeamV1Response,
	PremierTeamV1ResponseData,
	PremierTeamV1ResponseDataCustomization,
	PremierTeamV1ResponseDataPlacement,
	PremierTeamV1ResponseDataStats,
	SeasonIdShortCombo,
	StoredMMR,
	StoredMMRMap,
	StoredMMRSeason,
	StoredMMRTier,
	StoredMMRV2,
	TierIdNameCombo,
};
use mongodb::bson::{ doc, DateTime };
use mongodb::Client;
use serde_json::{ json, Value };
use uuid::Uuid;
use valorant_api::enums::kill_finish_damage::KillFinishingDamageType;
use valorant_api::enums::queue::Queue;
use valorant_api::response_types::competitive_updates_v1::CompetitiveUpdatesV1;
use valorant_api::response_types::leaderboard_v1::LeaderboardPlayer;
use valorant_api::response_types::matchdetails_v1::{ MatchDetailsV1, Player, PlayerLocation, PlayerRoundStats };

pub async fn parse_mmr_history_stored(entrys: Vec<LifetimeMMRHistory>) -> Vec<StoredMMR> {
	let mut entrys_ = vec![];
	for i in entrys.iter() {
		let map = get_map_by_id(i.map_id.as_str()).await;
		entrys_.push(StoredMMR {
			tier: StoredMMRTier {
				id: i.t,
				name: get_tier(i.t),
			},
			ranking_in_tier: i.r as u32,
			date: DateTime::from_millis((i.d as i64) * 1000)
				.try_to_rfc3339_string()
				.unwrap(),
			map: StoredMMRMap {
				id: i.map_id.clone(),
				name: if let Some(map) = map {
					map.displayName.clone()
				} else {
					"Unknown".to_string()
				},
			},
			match_id: i.m_id.clone(),
			elo: calculate_elo(i.t, i.r),
			season: StoredMMRSeason {
				id: i.s_id.clone(),
				short: match get_short_season_by_id(i.s_id.as_str()).await {
					Some(i) => i.short_id,
					None => "".to_string(),
				},
			},
			last_mmr_change: i.m_c,
		});
	}
	entrys_
}

pub async fn parse_mmr_history_v2_stored(entrys: Vec<LifetimeMMRHistory>) -> Vec<StoredMMRV2> {
	let mut entrys_ = vec![];
	for i in entrys.iter() {
		let map = get_map_by_id(i.map_id.as_str()).await;
		entrys_.push(StoredMMRV2 {
			tier: TierIdNameCombo {
				id: i.t,
				name: get_tier(i.t),
			},
			rr: i.r,
			date: DateTime::from_millis((i.d as i64) * 1000)
				.try_to_rfc3339_string()
				.unwrap(),
			map: MapIdNameCombo {
				id: i.map_id.clone(),
				name: if let Some(map) = map {
					map.displayName.clone()
				} else {
					"Unknown".to_string()
				},
			},
			match_id: i.m_id.clone(),
			elo: calculate_elo(i.t, i.r),
			season: SeasonIdShortCombo {
				id: i.s_id.clone(),
				short: match get_short_season_by_id(i.s_id.as_str()).await {
					Some(i) => i.short_id,
					None => "".to_string(),
				},
			},
			last_change: i.m_c,
			refunded_rr: if let Some(r_a) = i.r_a {
				r_a
			} else {
				0
			},
			was_derank_protected: if let Some(w_d_p) = i.w_d_p {
				w_d_p
			} else {
				false
			},
		});
	}
	entrys_
}

pub async fn parse_mmr_history(mmr_history: CompetitiveUpdatesV1, account: &Account) -> MMRHistoryV1Response {
	let mut matches = mmr_history.matches;
	matches.sort_by(|a, b| { b.match_start_time.timestamp_millis().cmp(&a.match_start_time.timestamp_millis()) });
	let mut mmr_history_data = vec![];
	for i in matches {
		let map = get_map_by_asset(i.map_id.as_str()).await;
		mmr_history_data.push(MMRHistoryV1Data {
			currenttier: i.tier_after_update as i32,
			currenttierpatched: get_tier(i.tier_after_update as i32),
			images: parse_mmr_images(&(i.tier_after_update as i32)),
			match_id: i.match_id.to_string(),
			map: if map.is_some() {
				let map = map.unwrap();
				MMRHistoryV1DataMap {
					id: map.uuid.clone(),
					name: map.displayName.clone(),
				}
			} else {
				MMRHistoryV1DataMap {
					id: i.map_id.clone(),
					name: "Unknown".to_string(),
				}
			},
			season_id: if i.season_id.is_some() {
				i.season_id.unwrap().to_string()
			} else {
				"Unknown".to_string()
			},
			ranking_in_tier: i.ranked_rating_after_update as i32,
			mmr_change_to_last_game: i.ranked_rating_earned,
			elo: calculate_elo(i.tier_after_update as i32, i.ranked_rating_after_update as i32),
			date: format_old_api_date(DateTime::from_millis(i.match_start_time.timestamp_millis())),
			date_raw: (i.match_start_time.timestamp_millis() / 1000) as i32,
		});
	}
	MMRHistoryV1Response {
		status: 200,
		name: account.name.clone(),
		tag: account.tag.clone(),
		data: mmr_history_data,
	}
}

pub async fn parse_mmr_history_v2(mmr_history: CompetitiveUpdatesV1, account: &Account) -> MMRHistoryV2Response {
	let mut matches = mmr_history.matches;
	matches.sort_by(|a, b| { b.match_start_time.timestamp_millis().cmp(&a.match_start_time.timestamp_millis()) });
	let mut mmr_history_data = vec![];
	for i in matches {
		let map = get_map_by_asset(i.map_id.as_str()).await;
		mmr_history_data.push(MMRHistoryV2History {
			tier: TierIdNameCombo {
				id: i.tier_after_update as i32,
				name: get_tier(i.tier_after_update as i32),
			},
			match_id: i.match_id.to_string(),
			map: if map.is_some() {
				let map = map.unwrap();
				MapIdNameCombo {
					id: map.uuid.clone(),
					name: map.displayName.clone(),
				}
			} else {
				MapIdNameCombo {
					id: i.map_id.clone(),
					name: "Unknown".to_string(),
				}
			},
			season: if i.season_id.is_some() {
				SeasonIdShortCombo {
					id: i.season_id.unwrap().to_string(),
					short: get_short_season_by_id(&i.season_id.unwrap().to_string()).await.unwrap().short_id,
				}
			} else {
				SeasonIdShortCombo {
					id: "".to_string(),
					short: "Unknown".to_string(),
				}
			},
			rr: i.ranked_rating_after_update as i32,
			last_change: i.ranked_rating_earned,
			elo: calculate_elo(i.tier_after_update as i32, i.ranked_rating_after_update as i32),
			refunded_rr: i.ranked_rating_refund_applied,
			was_derank_protected: i.was_derank_protected,
			date: DateTime::from_millis(i.match_start_time.timestamp_millis()).try_to_rfc3339_string().unwrap(),
		});
	}
	MMRHistoryV2Response {
		status: 200,
		data: MMRHistoryV2Data {
			account: MMRV3Account {
				puuid: account.puuid.clone(),
				name: account.name.clone(),
				tag: account.tag.clone(),
			},
			history: mmr_history_data,
		},
	}
}

pub async fn parse_mmr_v1(mmr_history: CompetitiveUpdatesV1, account: &Account) -> MMRV1Response {
	let mut matches = mmr_history.matches;
	matches.sort_by(|a, b| { b.match_start_time.timestamp_millis().cmp(&a.match_start_time.timestamp_millis()) });
	let last_match = matches.first().unwrap();
	let s = if last_match.season_id.is_some() { last_match.season_id.unwrap().to_string() } else { "Unknown".to_string() };
	let old = is_old(&s).await;
	MMRV1Response {
		status: 200,
		data: MMRV1Data {
			currenttier: last_match.tier_after_update as i32,
			currenttierpatched: if old {
				get_old_tier(last_match.tier_after_update as i32)
			} else {
				get_tier(last_match.tier_after_update as i32)
			},
			images: parse_mmr_images(&(last_match.tier_after_update as i32)),
			ranking_in_tier: last_match.ranked_rating_after_update as i32,
			mmr_change_to_last_game: last_match.ranked_rating_earned,
			elo: calculate_elo(last_match.tier_after_update as i32, last_match.ranked_rating_after_update as i32),
			name: account.name.clone(),
			tag: account.tag.clone(),
			old,
		},
	}
}

pub async fn parse_mmr_v2(mmr_history: CompetitiveUpdatesV1, mmr_data: PVPMMRPlayerV1, account: &Account) -> MMRV2Response {
	let mut matches = mmr_history.matches;
	matches.sort_by(|a, b| { b.match_start_time.timestamp_millis().cmp(&a.match_start_time.timestamp_millis()) });
	let last_match = matches.first().unwrap();
	let s = if last_match.season_id.is_some() { last_match.season_id.unwrap().to_string() } else { "Unknown".to_string() };
	let old = is_old(&s).await;

	//Parse Seasons & Highest rank
	let mut highest_rank: MMRV2HighestRank = MMRV2HighestRank {
		old: false,
		tier: 0,
		patched_tier: "Unset".to_string(),
		season: "null".to_string(),
	};
	let mut by_season: Value = json!({});
	let short_ids = get_short_ids().await;
	for season in short_ids.iter() {
		let season_comp_data = mmr_data.QueueSkills.get("competitive");
		if season_comp_data.is_none() {
			by_season[&season.short_id] = json!({
                "error": "No data available"
            });
			continue;
		}
		let season_comp_data = season_comp_data.unwrap();
		let seasonal_info_by_season_id = season_comp_data.SeasonalInfoBySeasonID.clone();
		if seasonal_info_by_season_id.is_none() {
			continue;
		}
		let season_data_option = seasonal_info_by_season_id.unwrap();
		let season_data_option = season_data_option.get(&season.season);
		if season_data_option.is_none() {
			by_season[&season.short_id] = json!({
                "error": "No data available"
            });
			continue;
		}
		let season_data = season_data_option.unwrap();
		if season_data.WinsByTier.clone().is_none() {
			by_season[&season.short_id] = json!({
                "error": "No data available"
            });
			continue;
		}
		let season_data_wins_by_tier = season_data.clone().WinsByTier.unwrap();
		let mut wins_by_tier: Vec<Value> = vec![];
		let old = is_old(&season.season).await;
		for (tier, wins) in season_data_wins_by_tier {
			let tier = tier.parse::<i32>().unwrap();
			if tier > highest_rank.tier {
				highest_rank = MMRV2HighestRank {
					old,
					tier,
					patched_tier: if old {
						get_old_tier(tier)
					} else {
						get_tier(tier)
					},
					season: season.short_id.clone(),
				};
			}
			for _i in 0..wins {
				wins_by_tier.push(
					json!({
                    "tier": tier,
                    "patched_tier": if old {
                        get_old_tier(tier)
                    } else {
                        get_tier(tier)
                    },
                })
				);
			}
		}
		wins_by_tier.sort_by(|a, b| { b["tier"].as_i64().unwrap().cmp(&a["tier"].as_i64().unwrap()) });
		by_season[&season.short_id] =
			json!({
            "wins": season_data.NumberOfWins,
            "number_of_games": season_data.NumberOfGames,
            "final_rank": season_data.CompetitiveTier,
            "final_rank_patched": if old {
                get_old_tier(season_data.CompetitiveTier)
            } else {
                get_tier(season_data.CompetitiveTier)
            },
            "act_rank_wins": wins_by_tier,
            "old": old
        });
	}
	MMRV2Response {
		status: 200,
		data: MMRV2Data {
			name: account.name.clone(),
			tag: account.tag.clone(),
			puuid: account.puuid.clone(),
			current_data: MMRV2CurrentData {
				currenttier: last_match.tier_after_update as i32,
				currenttierpatched: if old {
					get_old_tier(last_match.tier_after_update as i32)
				} else {
					get_tier(last_match.tier_after_update as i32)
				},
				images: parse_mmr_images(&(last_match.tier_after_update as i32)),
				ranking_in_tier: last_match.ranked_rating_after_update as i32,
				mmr_change_to_last_game: last_match.ranked_rating_earned,
				elo: calculate_elo(last_match.tier_after_update as i32, last_match.ranked_rating_after_update as i32),
				games_needed_for_rating: std::cmp::max(
					mmr_data.QueueSkills.get("competitive").unwrap().CurrentSeasonGamesNeededForRating,
					mmr_data.QueueSkills.get("competitive").unwrap().TotalGamesNeededForRating
				),
				old,
			},
			highest_rank,
			by_season,
		},
	}
}

pub async fn parse_mmr_v3(
	mmr_history: CompetitiveUpdatesV1,
	mmr_data: PVPMMRPlayerV1,
	account: &Account,
	client: &Client,
	affinity: &str,
	platform: &str
) -> MMRV3Response {
	let mut matches = mmr_history.matches;
	matches.sort_by(|a, b| { b.match_start_time.timestamp_millis().cmp(&a.match_start_time.timestamp_millis()) });
	let last_match = matches.first().unwrap();

	//Parse Seasons & Highest rank
	let mut peak = MMRV3Peak {
		tier: TierIdNameCombo {
			id: 0,
			name: "".to_string(),
		},
		season: SeasonIdShortCombo {
			id: "".to_string(),
			short: "".to_string(),
		},
		ranking_schema: "".to_string(),
		rr: 0,
	};
	let mut by_season: Vec<MMRV3Seasonal> = vec![];
	let short_ids = get_short_ids().await;
	let queue_skill_property = match RiotPlatforms::from_str(platform).unwrap() {
		RiotPlatforms::PC => "competitive",
		RiotPlatforms::CONSOLE => "console_competitive",
		_ => "competitive",
	};
	let season_comp_data = mmr_data.QueueSkills.get(queue_skill_property);
	for season in short_ids.iter() {
		if season_comp_data.is_none() {
			continue;
		}
		let season_comp_data = season_comp_data.unwrap();
		let seasonal_info_by_season_id = season_comp_data.SeasonalInfoBySeasonID.clone();
		if seasonal_info_by_season_id.is_none() {
			continue;
		}
		let season_data_option = seasonal_info_by_season_id.unwrap();
		let season_data_option = season_data_option.get(&season.season);
		if season_data_option.is_none() {
			continue;
		}
		let season_data = season_data_option.unwrap();
		if season_data.WinsByTier.clone().is_none() {
			continue;
		}
		let season_data_wins_by_tier = season_data.clone().WinsByTier.unwrap();
		let mut wins_by_tier: Vec<TierIdNameCombo> = vec![];
		let old = is_old(&season.season).await;
		for (tier, wins) in season_data_wins_by_tier {
			let tier = tier.parse::<i32>().unwrap();
			if tier > peak.tier.id {
				peak = MMRV3Peak {
					tier: TierIdNameCombo {
						id: tier,
						name: if old {
							get_old_tier(tier)
						} else {
							get_tier(tier)
						},
					},
					rr: season_data.RankedRating,
					season: SeasonIdShortCombo {
						id: season.season.clone(),
						short: season.short_id.clone(),
					},
					ranking_schema: if old {
						"base".to_string()
					} else {
						"ascendant".to_string()
					},
				};
			} else if tier >= peak.tier.id && season_data.RankedRating > peak.rr {
				peak = MMRV3Peak {
					tier: TierIdNameCombo {
						id: tier,
						name: if old {
							get_old_tier(tier)
						} else {
							get_tier(tier)
						},
					},
					rr: season_data.RankedRating,
					season: SeasonIdShortCombo {
						id: season.season.clone(),
						short: season.short_id.clone(),
					},
					ranking_schema: if old {
						"base".to_string()
					} else {
						"ascendant".to_string()
					},
				};
			}
			for _i in 0..wins {
				wins_by_tier.push(TierIdNameCombo {
					id: tier,
					name: if old {
						get_old_tier(tier)
					} else {
						get_tier(tier)
					},
				});
			}
		}
		wins_by_tier.sort_by(|a, b| b.id.cmp(&a.id));
		let leaderboard_placement_db = get_db::<LeaderboardPlayerDB>(&client, "leaderboard_players", None).find_one(
			doc! { "season": season.season.clone(), "affinity": affinity, "player.puuid": account.puuid.clone() }
		).await;
		let leaderboard_placement = if let Ok(leaderboard_placement_db) = leaderboard_placement_db {
			if let Some(leaderboard_placement) = leaderboard_placement_db { Some(leaderboard_placement) } else { None }
		} else {
			None
		};
		by_season.push(MMRV3Seasonal {
			season: SeasonIdShortCombo {
				id: season.season.clone(),
				short: season.short_id.clone(),
			},
			wins: season_data.NumberOfWins,
			games: season_data.NumberOfGames,
			end_tier: TierIdNameCombo {
				id: season_data.CompetitiveTier,
				name: if old {
					get_old_tier(season_data.CompetitiveTier)
				} else {
					get_tier(season_data.CompetitiveTier)
				},
			},
			end_rr: season_data.RankedRating,
			ranking_schema: if old {
				"base".to_string()
			} else {
				"ascendant".to_string()
			},
			leaderboard_placement: if let Some(leaderboard_placement) = leaderboard_placement {
				Some(MMRV3LeaderboardPlacement {
					rank: leaderboard_placement.player.leaderboard_rank,
					updated_at: leaderboard_placement.updated_at.clone().try_to_rfc3339_string().unwrap(),
				})
			} else {
				None
			},
			act_wins: wins_by_tier,
		});
	}

	let c_season_ = get_c_season().await.to_string();
	let c_season = by_season.iter().find(|i| i.season.id == c_season_);
	MMRV3Response {
		status: 200,
		data: MMRV3Data {
			account: MMRV3Account {
				puuid: account.puuid.clone(),
				name: account.name.clone(),
				tag: account.tag.clone(),
			},
			peak: if peak.tier.id == 0 {
				None
			} else {
				Some(peak)
			},
			current: MMRV3Current {
				tier: TierIdNameCombo {
					id: last_match.tier_after_update as i32,
					name: get_tier(last_match.tier_after_update as i32),
				},
				rr: last_match.ranked_rating_after_update as i32,
				last_change: last_match.ranked_rating_earned,
				elo: calculate_elo(last_match.tier_after_update as i32, last_match.ranked_rating_after_update as i32),
				games_needed_for_rating: std::cmp::max(
					mmr_data.QueueSkills.get(queue_skill_property).unwrap().CurrentSeasonGamesNeededForRating,
					mmr_data.QueueSkills.get(queue_skill_property).unwrap().TotalGamesNeededForRating
				),
				rank_protection_shields: if get_tier_protection_status(last_match.tier_after_update as i32) {
					mmr_data.DerankProtectedGamesRemaining
				} else {
					2
				},
				leaderboard_placement: if let Some(c_season) = c_season {
					c_season.leaderboard_placement.clone()
				} else {
					None
				},
			},
			seasonal: by_season,
		},
	}
}

pub async fn parse_match_v2(match_: MatchDetailsV1, affinity: &str) -> MatchesV2Data {
	let map = get_map_by_asset(match_.match_info.map_id.as_str()).await;
	if match_.match_info.queue_id.is_none() {
		eprintln!("Queue ID is missing for match {}", match_.match_info.match_id);
	}
	let queue_id = if match_.match_info.queue_id.is_none() { "".to_string() } else { match_.match_info.queue_id.unwrap().to_string() };
	let queue = get_queue_by_queue_id(&queue_id).await;
	let gamemode = get_gamemode_by_partial_asset(match_.match_info.game_mode.as_str()).await;
	let gamepod = get_gamepod_by_id(match_.match_info.game_pod_id.as_str()).await;
	let metadata = MatchesV2DataMetadata {
		map: if let Some(map) = map {
			Some(map.displayName.clone())
		} else {
			None
		},
		game_version: match_.match_info.game_version,
		game_length: match_.match_info.game_length_millis.unwrap() / 1000,
		game_start: match_.match_info.game_start_millis / 1000,
		game_start_patched: format_old_api_date(DateTime::from_millis(match_.match_info.game_start_millis as i64)),
		rounds_played: match_.teams.clone().unwrap().first().unwrap().rounds_played,
		mode: if let Some(queue) = queue {
			Some(queue.displayName.unwrap())
		} else {
			None
		},
		mode_id: if queue_id.len() <= 0 {
			"custom".to_string()
		} else {
			queue_id
		},
		queue: if let Some(gamemode) = gamemode {
			Some(gamemode.displayName)
		} else {
			None
		},
		season_id: match_.match_info.season_id.to_string(),
		platform: match_.match_info.platform_type,
		matchid: match_.match_info.match_id.to_string(),
		premier_info: MatchesV2DataMetadataPremierInfo {
			tournament_id: if let Some(_premier_info) = match_.match_info.premier_match_info.clone() {
				None //TODO: Get Tournament ID by "premierEventId"
			} else {
				None
			},
			matchup_id: if let Some(_premier_info) = match_.match_info.premier_match_info {
				None //TODO: Get MatchUP ID by "premierEventId"
			} else {
				None
			},
		},
		region: Some(affinity.to_string()),
		cluster: if let Some(gamepod) = gamepod {
			Some(gamepod)
		} else {
			None
		},
	};
	let mut players_ = vec![];
	for i in match_.players.iter().filter(|i| !i.is_observer.unwrap_or(false)) {
		/*if i.stats.is_none() || i.stats.unwrap().ability_casts.is_none() {
            warn!("Stats are missing for player {} in match {}", i.subject.clone(), match_.match_info.match_id);
        }*/
		let agent = get_agent_by_id(&i.character_id.unwrap().to_string()).await;
		let agent = if let Some(agent) = agent { agent.displayName } else { "Unknown".to_string() };
		let ability_casts = i.stats.unwrap().ability_casts;
		let stats = i.stats.unwrap();
		let player = MatchesV2DataPlayer {
			puuid: i.subject.clone().to_string(),
			name: i.game_name.clone(),
			tag: i.tag_line.clone(),
			team: i.team_id.clone().to_string(),
			level: i.account_level.unwrap_or(0),
			character: Some(agent),
			currenttier: i.competitive_tier,
			currenttier_patched: get_tier(i.competitive_tier as i32),
			player_card: i.player_card.to_string(),
			player_title: i.player_title.to_string(),
			party_id: i.party_id.to_string(),
			session_playtime: MatchesV2DataPlayerSessionPlaytime {
				minutes: i.session_playtime_minutes.unwrap_or(0),
				seconds: i.session_playtime_minutes.unwrap_or(0) * 60,
				milliseconds: i.session_playtime_minutes.unwrap_or(0) * 60000,
			},
			behavior: if let Some(b) = i.behavior_factors {
				MatchesV2DataPlayerBehavior {
					afk_rounds: b.afk_rounds,
					friendly_fire: MatchesV2DataPlayerBehaviorFriendlyFire {
						incoming: b.friendly_fire_incoming,
						outgoing: b.friendly_fire_outgoing,
					},
					rounds_in_spawn: b.stayed_in_spawn_rounds,
				}
			} else {
				MatchesV2DataPlayerBehavior {
					afk_rounds: 0.0,
					friendly_fire: MatchesV2DataPlayerBehaviorFriendlyFire {
						incoming: Some(0.0),
						outgoing: Some(0.0),
					},
					rounds_in_spawn: Some(0.0),
				}
			},
			platform: MatchesV2DataPlatform {
				type_: i.platform_info.platform_type.clone(),
				os: MatchesV2DataPlatformOs {
					name: i.platform_info.platform_os.clone(),
					version: i.platform_info.platform_os_version.clone(),
				},
			},
			ability_casts: MatchesV2DataPlayerAbilityCasts {
				x_cast: if ability_casts.is_none() {
					None
				} else {
					Some(ability_casts.unwrap().ultimate_casts)
				},
				e_cast: if ability_casts.is_none() {
					None
				} else {
					Some(ability_casts.unwrap().ability2_casts)
				},
				q_cast: if ability_casts.is_none() {
					None
				} else {
					Some(ability_casts.unwrap().ability1_casts)
				},
				c_cast: if ability_casts.is_none() {
					None
				} else {
					Some(ability_casts.unwrap().grenade_casts)
				},
			},
			assets: MatchesV2DataPlayerAssets {
				card: MatchesV2DataPlayerAssetsCard {
					small: format!("https://media.valorant-api.com/playercards/{}/smallart.png", i.player_card),
					large: format!("https://media.valorant-api.com/playercards/{}/largeart.png", i.player_card),
					wide: format!("https://media.valorant-api.com/playercards/{}/wideart.png", i.player_card),
				},
				agent: MatchesV2DataPlayerAssetsAgent {
					small: format!("https://media.valorant-api.com/agents/{}/displayicon.png", i.character_id.unwrap()),
					bust: format!("https://media.valorant-api.com/agents/{}/fullportrait.png", i.character_id.unwrap()),
					full: format!("https://media.valorant-api.com/agents/{}/fullportrait.png", i.character_id.unwrap()),
					killfeed: format!("https://media.valorant-api.com/agents/{}/killfeedportrait.png", i.character_id.unwrap()),
				},
			},
			stats: MatchesV2DataPlayerStats {
				score: stats.score,
				kills: stats.kills,
				deaths: stats.deaths,
				assists: stats.assists,
				bodyshots: 0,
				headshots: 0,
				legshots: 0,
			},
			economy: MatchesV2DataPlayerEconomy {
				spent: MatchesV2DataPlayerEconomyValue {
					overall: 0,
					average: 0.0,
				},
				loadout_value: MatchesV2DataPlayerEconomyValue {
					overall: 0,
					average: 0.0,
				},
			},
			damage_made: 0,
			damage_received: 0,
		};
		players_.push(player);
	}
	let mut observers_ = vec![];
	for i in match_.players.iter().filter(|i| i.is_observer.unwrap_or(false)) {
		let observer = MatchesV2DataObserver {
			puuid: i.subject.clone().to_string(),
			name: i.game_name.clone(),
			tag: i.tag_line.clone(),
			platform: MatchesV2DataPlatform {
				type_: i.platform_info.platform_type.clone(),
				os: MatchesV2DataPlatformOs {
					name: i.platform_info.platform_os.clone(),
					version: i.platform_info.platform_os_version.clone(),
				},
			},
			session_playtime: MatchesV2DataPlayerSessionPlaytime {
				minutes: i.session_playtime_minutes.unwrap_or(0),
				seconds: i.session_playtime_minutes.unwrap_or(0) * 60,
				milliseconds: i.session_playtime_minutes.unwrap_or(0) * 60000,
			},
			team: i.team_id.clone().to_string(),
			level: i.account_level.unwrap_or(0),
			player_card: i.player_card.to_string(),
			player_title: i.player_title.to_string(),
			party_id: i.party_id.to_string(),
		};
		observers_.push(observer);
	}
	let coaches = match_.coaches
		.iter()
		.map(|i| MatchesV2DataCoach {
			puuid: i.subject.to_string(),
			team: i.team_id.clone().to_string(),
		})
		.collect::<Vec<MatchesV2DataCoach>>();
	let teams: MatchesV2DataTeams = {
		if
			match_.match_info.queue_id.unwrap_or(Queue::Tournamentmode) == Queue::Deathmatch ||
			match_.match_info.queue_id.unwrap_or(Queue::Tournamentmode) == Queue::ConsoleDeathmatch
		{
			MatchesV2DataTeams {
				red: MatchesV2DataTeam {
					has_won: None,
					rounds_won: None,
					rounds_lost: None,
					roster: None,
				},
				blue: MatchesV2DataTeam {
					has_won: None,
					rounds_won: None,
					rounds_lost: None,
					roster: None,
				},
			}
		} else {
			let teams = match_.teams.clone().unwrap();
			let team_red = teams.iter().find(|j| j.team_id.to_string() == "Red");
			let team_blue = teams.iter().find(|j| j.team_id.to_string() == "Blue");
			MatchesV2DataTeams {
				red: if let Some(team) = team_red {
					let team_blue = team_blue.unwrap();
					MatchesV2DataTeam {
						has_won: Some(team.won),
						rounds_won: Some(team.num_points),
						rounds_lost: Some(team_blue.num_points),
						roster: if let Some(roster) = team.roster_info.clone() {
							let primary = premier_extract_hex_color(&roster.primary_color).await;
							let secondary = premier_extract_hex_color(&roster.secondary_color).await;
							let tertiary = premier_extract_hex_color(&roster.tertiary_color).await;
							Some(MatchesV2DataTeamRoster {
								id: roster.roster_id.to_string(),
								members: roster.members
									.iter()
									.map(|x| x.subject.to_string())
									.collect::<Vec<String>>(),
								name: roster.name.clone(),
								tag: roster.tag.clone(),
								customization: MatchesV2DataTeamRosterCustomization {
									icon: roster.icon.clone(),
									image: format!(
										"https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/{}?primary={}&secondary={}&tertiary={}",
										roster.icon,
										primary,
										secondary,
										tertiary
									),
									primary_color: format!("#{}", primary),
									secondary_color: format!("#{}", secondary),
									tertiary_color: format!("#{}", tertiary),
								},
							})
						} else {
							None
						},
					}
				} else {
					MatchesV2DataTeam {
						has_won: None,
						rounds_won: None,
						rounds_lost: None,
						roster: None,
					}
				},
				blue: if let Some(team) = team_blue {
					let team_red = team_red.unwrap();
					MatchesV2DataTeam {
						has_won: Some(team.won),
						rounds_won: Some(team.num_points),
						rounds_lost: Some(team_red.num_points),
						roster: if let Some(roster) = team.roster_info.clone() {
							let primary = premier_extract_hex_color(&roster.primary_color).await;
							let secondary = premier_extract_hex_color(&roster.secondary_color).await;
							let tertiary = premier_extract_hex_color(&roster.tertiary_color).await;
							Some(MatchesV2DataTeamRoster {
								id: roster.roster_id.to_string(),
								members: roster.members
									.iter()
									.map(|x| x.subject.to_string())
									.collect::<Vec<String>>(),
								name: roster.name.clone(),
								tag: roster.tag.clone(),
								customization: MatchesV2DataTeamRosterCustomization {
									icon: roster.icon.clone(),
									image: format!(
										"https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/{}?primary={}&secondary={}&tertiary={}",
										roster.icon,
										primary,
										secondary,
										tertiary
									),
									primary_color: format!("#{}", primary),
									secondary_color: format!("#{}", secondary),
									tertiary_color: format!("#{}", tertiary),
								},
							})
						} else {
							None
						},
					}
				} else {
					MatchesV2DataTeam {
						has_won: None,
						rounds_won: None,
						rounds_lost: None,
						roster: None,
					}
				},
			}
		}
	};
	let mut rounds_ = vec![];
	for i in match_.round_results.unwrap() {
		rounds_.push(MatchesV2DataRound {
			winning_team: i.winning_team.to_string(),
			end_type: i.round_result.clone(),
			bomb_planted: i.bomb_planter.is_some(),
			bomb_defused: i.bomb_defuser.is_some(),
			plant_events: MatchesV2DataRoundPlantEvents {
				plant_location: if i.bomb_planter.is_some() {
					Some(MatchesV2DataRoundEventLocation {
						x: i.plant_location.unwrap().x,
						y: i.plant_location.unwrap().y,
					})
				} else {
					None
				},
				planted_by: if i.bomb_planter.is_some() {
					Some(parse_round_player(&match_.players, &i.bomb_planter.unwrap().to_string()))
				} else {
					None
				},
				plant_site: if i.bomb_planter.is_some() {
					Some(i.plant_site.unwrap().to_string())
				} else {
					None
				},
				plant_time_in_round: if i.bomb_planter.is_some() {
					Some(i.plant_round_time.unwrap())
				} else {
					None
				},
				player_locations_on_plant: if i.bomb_planter.is_some() {
					Some(parse_locations(&match_.players, &i.plant_player_locations.clone().unwrap()))
				} else {
					None
				},
			},
			defuse_events: MatchesV2DataRoundDefuseEvents {
				defuse_location: if i.bomb_defuser.is_some() {
					Some(MatchesV2DataRoundEventLocation {
						x: i.defuse_location.unwrap().x,
						y: i.defuse_location.unwrap().y,
					})
				} else {
					None
				},
				defused_by: if i.bomb_defuser.is_some() {
					Some(parse_round_player(&match_.players, &i.bomb_defuser.unwrap().to_string()))
				} else {
					None
				},
				defuse_time_in_round: if i.bomb_defuser.is_some() {
					Some(i.defuse_round_time.unwrap())
				} else {
					None
				},
				player_locations_on_defuse: if i.bomb_defuser.is_some() {
					Some(parse_locations(&match_.players, &i.defuse_player_locations.clone().unwrap()))
				} else {
					None
				},
			},
			player_stats: parse_player_stats(&i.player_stats, &match_.players, &mut players_).await,
		});
	}

	let mut kills_ = vec![];
	if let Some(kills) = match_.kills {
		for i in kills {
			let killer = parse_round_player(&match_.players, &i.killer.unwrap_or(Uuid::new_v4()).to_string());
			let victim = parse_round_player(&match_.players, &i.victim.to_string());
			let locations = parse_locations(&match_.players, &i.player_locations);
			let assistants: Vec<MatchesV2DataRoundPlayerStatsKillEventsAssistants> = i.assistants
				.iter()
				.map(|j| {
					let assistant = parse_round_player(&match_.players, &j.to_string());
					MatchesV2DataRoundPlayerStatsKillEventsAssistants {
						assistant_puuid: assistant.puuid,
						assistant_display_name: assistant.display_name,
						assistant_team: assistant.team,
					}
				})
				.collect::<Vec<MatchesV2DataRoundPlayerStatsKillEventsAssistants>>();

			let weapons = get_weapons().await;
			let weapon = if i.finishing_damage.damage_type.clone().is_some() && i.finishing_damage.damage_type.clone().unwrap() == KillFinishingDamageType::Weapon {
				let weapon = weapons.iter().find(|k| { k.uuid.to_string() == i.finishing_damage.damage_item.clone().unwrap().to_string() });
				if let Some(weapon) = weapon {
					Some(weapon)
				} else {
					None
				}
			} else {
				None
			};

			kills_.push(MatchesV2DataKill {
				kill_time_in_round: i.round_time,
				kill_time_in_match: i.game_time,
				round: i.round,
				killer_puuid: killer.puuid,
				killer_display_name: killer.display_name,
				killer_team: killer.team,
				victim_puuid: victim.puuid,
				victim_display_name: victim.display_name,
				victim_team: victim.team,
				victim_death_location: MatchesV2DataRoundEventLocation {
					x: i.victim_location.x,
					y: i.victim_location.y,
				},
				damage_weapon_id: if weapon.is_some() {
					weapon.unwrap().uuid.to_string()
				} else {
					"".to_string()
				},
				damage_weapon_name: if weapon.is_some() {
					Some(weapon.unwrap().display_name.clone())
				} else {
					None
				},
				damage_weapon_assets: MatchesV2DataRoundPlayerStatsKillEventsAssets {
					display_icon: if weapon.is_some() {
						Some(weapon.unwrap().display_icon.clone().unwrap())
					} else {
						None
					},
					killfeed_icon: if weapon.is_some() {
						Some(weapon.unwrap().kill_stream_icon.clone())
					} else {
						None
					},
				},
				secondary_fire_mode: i.finishing_damage.is_secondary_fire_mode,
				player_locations_on_kill: locations,
				assistants,
			});
		}
	}
	//calculate averages for economy & loadout value
	for player in players_.iter_mut() {
		player.economy.spent.average = (player.economy.spent.overall as f32) / (metadata.rounds_played as f32);
		player.economy.loadout_value.average = (player.economy.loadout_value.overall as f32) / (metadata.rounds_played as f32);
	}
	MatchesV2Data {
		metadata,
		players: MatchesV2DataPlayers {
			all_players: players_.clone(),
			red: players_
				.clone()
				.iter()
				.filter(|i| i.team == "Red")
				.map(|i| i.clone())
				.collect::<Vec<MatchesV2DataPlayer>>(),
			blue: players_
				.iter()
				.filter(|i| i.team == "Blue")
				.map(|i| i.clone())
				.collect::<Vec<MatchesV2DataPlayer>>(),
		},
		observers: observers_,
		coaches,
		teams,
		rounds: rounds_,
		kills: kills_,
	}
}

pub async fn parse_match_v4(match_: MatchDetailsV1, affinity: &str) -> MatchesV4Data {
	let map = get_map_by_asset(match_.match_info.map_id.as_str()).await;
	let queue_string = if let Some(queue) = match_.match_info.queue_id { queue.to_string() } else { "custom".to_string() };
	let queue = get_queue_by_queue_id(&queue_string).await;
	let gamemode = get_gamemode_by_partial_asset(match_.match_info.game_mode.as_str()).await;
	let gamepod = get_gamepod_by_id(match_.match_info.game_pod_id.as_str()).await;
	let season = get_short_season_by_id(&match_.match_info.season_id.to_string()).await;
	let metadata = MatchesV4DataMetadata {
		match_id: match_.match_info.match_id.to_string(),
		map: MapIdNameCombo {
			id: if let Some(map) = map.clone() {
				map.uuid.to_string()
			} else {
				"Unknown".to_string()
			},
			name: if let Some(map) = map {
				map.displayName.clone()
			} else {
				"Unknown".to_string()
			},
		},
		game_version: match_.match_info.game_version,
		game_length_in_ms: match_.match_info.game_length_millis.unwrap_or(0),
		started_at: DateTime::from_millis(match_.match_info.game_start_millis as i64)
			.try_to_rfc3339_string()
			.unwrap(),
		is_completed: match_.match_info.is_completed,
		queue: MatchesV4DataMetadataQueue {
			id: queue_string,
			name: if let Some(queue) = queue {
				queue.displayName
			} else {
				None
			},
			mode_type: if let Some(gamemode) = gamemode {
				Some(gamemode.displayName)
			} else {
				None
			},
		},
		season: SeasonIdShortCombo {
			id: match_.match_info.season_id.to_string(),
			short: if let Some(season) = season {
				season.short_id
			} else {
				"Unknown".to_string()
			},
		},
		platform: match_.match_info.platform_type,
		premier: None,
		region: Some(affinity.to_string()),
		cluster: if let Some(gamepod) = gamepod {
			Some(gamepod)
		} else {
			None
		},
		party_rr_penaltys: {
			if match_.match_info.party_rr_penalties.is_none() {
				vec![]
			} else {
				let mut penalties: Vec<MatchesV4DataMetadataPartyRRPenalty> = vec![];
				for (key, value) in match_.match_info.party_rr_penalties.unwrap() {
					penalties.push(MatchesV4DataMetadataPartyRRPenalty {
						party_id: key.to_string(),
						penalty: value,
					});
				}
				penalties
			}
		},
	};

	let mut players_ = vec![];
	for i in match_.players.iter().filter(|i| !i.is_observer.unwrap_or(false)) {
		let agent = get_agent_by_id(&i.character_id.unwrap().to_string()).await;
		let ability_casts = i.stats.unwrap().ability_casts;
		let stats = i.stats.unwrap();
		let player = MatchesV4DataPlayer {
			puuid: i.subject.clone().to_string(),
			name: i.game_name.clone(),
			tag: i.tag_line.clone(),
			team_id: i.team_id.clone().to_string(),
			platform: i.platform_info.platform_type.clone(),
			account_level: i.account_level.unwrap_or(0),
			agent: AgentIdNameCombo {
				id: i.character_id.unwrap().to_string(),
				name: if let Some(agent) = agent {
					agent.displayName
				} else {
					"Unknown".to_string()
				},
			},
			tier: TierIdNameCombo {
				id: i.competitive_tier as i32,
				name: get_tier(i.competitive_tier as i32),
			},
			party_id: i.party_id.to_string(),
			session_playtime_in_ms: i.session_playtime_minutes.unwrap_or(0) * 60000,
			behavior: if let Some(b) = i.behavior_factors {
				MatchesV4DataPlayerBehavior {
					afk_rounds: b.afk_rounds,
					friendly_fire: MatchesV4DataPlayerBehaviorFriendlyFire {
						incoming: b.friendly_fire_incoming.unwrap_or(0.0),
						outgoing: b.friendly_fire_outgoing.unwrap_or(0.0),
					},
					rounds_in_spawn: b.stayed_in_spawn_rounds.unwrap_or(0.0),
				}
			} else {
				MatchesV4DataPlayerBehavior {
					afk_rounds: 0.0,
					friendly_fire: MatchesV4DataPlayerBehaviorFriendlyFire {
						incoming: 0.0,
						outgoing: 0.0,
					},
					rounds_in_spawn: 0.0,
				}
			},
			ability_casts: MatchesV4DataPlayerAbilityCasts {
				ultimate: if let Some(ability_casts) = ability_casts {
					Some(ability_casts.ultimate_casts)
				} else {
					None
				},
				ability1: if let Some(ability_casts) = ability_casts {
					Some(ability_casts.ability1_casts)
				} else {
					None
				},
				ability2: if let Some(ability_casts) = ability_casts {
					Some(ability_casts.ability2_casts)
				} else {
					None
				},
				grenade: if let Some(ability_casts) = ability_casts {
					Some(ability_casts.grenade_casts)
				} else {
					None
				},
			},
			customization: MatchesV4DataPlayerCustomization {
				card: i.player_card.to_string(),
				title: i.player_title.to_string(),
				preferred_level_border: if let Some(level_border) = i.preferred_level_border {
					Some(level_border.to_string())
				} else {
					None
				},
			},
			stats: MatchesV4DataPlayerStats {
				score: stats.score,
				kills: stats.kills,
				deaths: stats.deaths,
				assists: stats.assists,
				bodyshots: 0,
				headshots: 0,
				legshots: 0,
				damage: MatchesV4DataPlayerStatsDamage {
					dealt: 0,
					received: 0,
				},
			},
			economy: MatchesV4DataPlayerEconomy {
				spent: MatchesV4DataPlayerEconomySpent {
					overall: 0,
					average: 0.0,
				},
				loadout_value: MatchesV4DataPlayerEconomyLoadoutValue {
					overall: 0,
					average: 0.0,
				},
			},
		};
		players_.push(player);
	}
	let mut observers_ = vec![];
	for i in match_.players.iter().filter(|i| i.is_observer.unwrap_or(false)) {
		let observer = MatchesV4DataObserver {
			puuid: i.subject.clone().to_string(),
			name: i.game_name.clone(),
			tag: i.tag_line.clone(),
			account_level: i.account_level.unwrap_or(0),
			session_playtime_in_ms: i.session_playtime_minutes.unwrap_or(0) * 60000,
			card_id: i.player_card.to_string(),
			title_id: i.player_title.to_string(),
			party_id: i.party_id.to_string(),
		};
		observers_.push(observer);
	}
	let mut coaches_ = vec![];
	for i in match_.coaches.iter() {
		let coach = MatchesV4DataCoach {
			puuid: i.subject.clone().to_string(),
			team_id: i.team_id.clone().to_string(),
		};
		coaches_.push(coach);
	}
	let mut teams: Vec<MatchesV4DataTeam> = vec![];
	let base_teams = match_.teams.clone().unwrap();
	let team_len = base_teams.len();
	for i in &base_teams {
		if team_len == 2 {
			let other_team = base_teams.iter().find(|j| j.team_id != i.team_id);
			let other_team = if let Some(other_team) = other_team {
				other_team
			} else {
				continue;
			};
			teams.push(MatchesV4DataTeam {
				team_id: i.team_id.clone().to_string(),
				rounds: MatchesV4DataTeamRounds {
					won: i.num_points,
					lost: other_team.num_points,
				},
				won: i.won,
				premier_roster: if let Some(roster) = i.roster_info.clone() {
					let primary = premier_extract_hex_color(&roster.primary_color).await;
					let secondary = premier_extract_hex_color(&roster.secondary_color).await;
					let tertiary = premier_extract_hex_color(&roster.tertiary_color).await;
					Some(MatchesV4DataTeamPremierRoster {
						id: roster.roster_id.to_string(),
						members: roster.members
							.iter()
							.map(|x| x.subject.to_string())
							.collect::<Vec<String>>(),
						name: roster.name.clone(),
						tag: roster.tag.clone(),
						customization: MatchesV4DataTeamPremierRosterCustomization {
							icon: roster.icon.clone(),
							image: format!(
								"https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/{}?primary={}&secondary={}&tertiary={}",
								roster.icon,
								primary,
								secondary,
								tertiary
							),
							primary_color: format!("#{}", primary),
							secondary_color: format!("#{}", secondary),
							tertiary_color: format!("#{}", tertiary),
						},
					})
				} else {
					None
				},
			});
		} else if
			match_.match_info.queue_id.unwrap_or(Queue::Tournamentmode) == Queue::Deathmatch ||
			match_.match_info.queue_id.unwrap_or(Queue::Tournamentmode) == Queue::ConsoleDeathmatch
		{
			teams.push(MatchesV4DataTeam {
				team_id: i.team_id.clone().to_string(),
				rounds: MatchesV4DataTeamRounds {
					won: i.num_points,
					lost: 0,
				},
				won: i.won,
				premier_roster: None,
			});
		} else {
			teams.push(MatchesV4DataTeam {
				team_id: i.team_id.clone().to_string(),
				rounds: MatchesV4DataTeamRounds {
					won: i.num_points,
					lost: i.rounds_played - i.rounds_won,
				},
				won: i.won,
				premier_roster: if let Some(roster) = i.roster_info.clone() {
					let primary = premier_extract_hex_color(&roster.primary_color).await;
					let secondary = premier_extract_hex_color(&roster.secondary_color).await;
					let tertiary = premier_extract_hex_color(&roster.tertiary_color).await;
					Some(MatchesV4DataTeamPremierRoster {
						id: roster.roster_id.to_string(),
						members: roster.members
							.iter()
							.map(|x| x.subject.to_string())
							.collect::<Vec<String>>(),
						name: roster.name.clone(),
						tag: roster.tag.clone(),
						customization: MatchesV4DataTeamPremierRosterCustomization {
							icon: roster.icon.clone(),
							image: format!(
								"https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/{}?primary={}&secondary={}&tertiary={}",
								roster.icon,
								primary,
								secondary,
								tertiary
							),
							primary_color: format!("#{}", primary),
							secondary_color: format!("#{}", secondary),
							tertiary_color: format!("#{}", tertiary),
						},
					})
				} else {
					None
				},
			});
		}
	}

	let mut rounds_ = vec![];
	for i in match_.round_results.unwrap() {
		rounds_.push(MatchesV4DataRound {
			id: i.round_num,
			result: i.round_result_code,
			ceremony: i.round_ceremony,
			winning_team: i.winning_team.to_string(),
			plant: if i.plant_site.is_some() {
				Some(MatchesV4DataRoundPlant {
					round_time_in_ms: i.plant_round_time.unwrap() as i32,
					location: MatchesV4DataRoundLocation {
						x: i.plant_location.unwrap().x,
						y: i.plant_location.unwrap().y,
					},
					player: parse_round_player_v4(&match_.players, &i.bomb_planter.unwrap().to_string()),
					site: i.plant_site.unwrap().to_string(),
					player_locations: parse_locations_v4(&match_.players, &i.plant_player_locations.unwrap()),
				})
			} else {
				None
			},
			defuse: if i.defuse_player_locations.is_some() {
				Some(MatchesV4DataRoundDefuse {
					round_time_in_ms: i.defuse_round_time.unwrap() as i32,
					location: MatchesV4DataRoundLocation {
						x: i.defuse_location.unwrap().x,
						y: i.defuse_location.unwrap().y,
					},
					player: parse_round_player_v4(&match_.players, &i.bomb_defuser.unwrap().to_string()),
					player_locations: parse_locations_v4(&match_.players, &i.defuse_player_locations.unwrap()),
				})
			} else {
				None
			},
			stats: parse_player_stats_v4(&i.player_stats, &match_.players, &mut players_).await,
		});
	}

	let mut kills_ = vec![];
	if let Some(kills) = match_.kills {
		for i in kills {
			let killer = parse_round_player_v4(&match_.players, &i.killer.unwrap_or(Uuid::new_v4()).to_string());
			let victim = parse_round_player_v4(&match_.players, &i.victim.to_string());
			let locations = parse_locations_v4(&match_.players, &i.player_locations);
			let assistants: Vec<MatchesV4DataRoundPlayer> = i.assistants
				.iter()
				.map(|j| parse_round_player_v4(&match_.players, &j.to_string()))
				.collect::<Vec<MatchesV4DataRoundPlayer>>();

			let weapons = get_weapons().await;
			let agents = get_agents().await;

			kills_.push(MatchesV4DataKill {
				time_in_round_in_ms: i.round_time,
				time_in_match_in_ms: i.game_time,
				round: i.round,
				killer,
				victim,
				location: MatchesV4DataRoundLocation {
					x: i.victim_location.x,
					y: i.victim_location.y,
				},
				secondary_fire_mode: i.finishing_damage.is_secondary_fire_mode,
				assistants,
				weapon: MatchesV4DataRoundPlayerStatsEconomyWeapon {
					id: match i.finishing_damage.damage_type.clone() {
						Some(v) =>
							match v {
								KillFinishingDamageType::Weapon => Some(i.finishing_damage.damage_item.clone().unwrap().to_string().to_lowercase()),
								_ => Some(i.finishing_damage.damage_item.clone().unwrap().to_string()),
							}
						None => None,
					},
					name: match i.finishing_damage.damage_type.clone() {
						Some(v) =>
							match v {
								KillFinishingDamageType::Weapon => {
									let weapon = weapons.iter().find(|k| { k.uuid.to_string() == i.finishing_damage.damage_item.clone().unwrap().to_string() });
									if let Some(weapon) = weapon {
										Some(weapon.display_name.clone())
									} else {
										None
									}
								}
								KillFinishingDamageType::Ability => {
									let extended_killer = if let Some(killer) = i.killer { players_.iter().find(|j| j.puuid == killer.to_string()) } else { None };
									if extended_killer.is_some() {
										let agent = agents.iter().find(|j| j.uuid == extended_killer.unwrap().agent.id);
										if let Some(agent) = agent {
											let ability = agent.abilities.iter().find(|j| { j.slot == i.finishing_damage.damage_item.clone().unwrap().to_string() });
											if let Some(ability) = ability {
												Some(ability.displayName.clone())
											} else {
												None
											}
										} else {
											None
										}
									} else {
										None
									}
								}
								_ => None,
							}
						None => None,
					},
					type_: match i.finishing_damage.damage_type {
						Some(v) => Some(v.to_string()),
						None => None,
					},
				},
				player_locations: locations,
			});
		}
	}
	//calculate averages for economy & loadout value
	let rounds__ = {
		let team_1 = teams.first().unwrap().clone().rounds;
		team_1.won + team_1.lost
	};
	for player in players_.iter_mut() {
		player.economy.spent.average = (player.economy.spent.overall as f32) / (rounds__ as f32);
		player.economy.loadout_value.average = (player.economy.loadout_value.overall as f32) / (rounds__ as f32);
	}
	MatchesV4Data {
		metadata,
		players: players_,
		observers: observers_,
		coaches: coaches_,
		teams,
		rounds: rounds_,
		kills: kills_,
	}
}

pub fn parse_round_player(players: &Vec<Player>, puuid: &str) -> MatchesV2DataRoundPlayer {
	let player = players.iter().find(|i| i.subject.to_string() == puuid);
	if let Some(player) = player {
		MatchesV2DataRoundPlayer {
			puuid: player.subject.to_string(),
			display_name: format!("{}#{}", player.game_name, player.tag_line),
			team: player.team_id.to_string(),
		}
	} else {
		MatchesV2DataRoundPlayer {
			puuid: puuid.to_string(),
			display_name: "Unknown".to_string(),
			team: "Unknown".to_string(),
		}
	}
}

pub fn parse_round_player_v4(players: &Vec<Player>, puuid: &str) -> MatchesV4DataRoundPlayer {
	let player = players.iter().find(|i| i.subject.to_string() == puuid);
	if let Some(player) = player {
		MatchesV4DataRoundPlayer {
			puuid: player.subject.to_string(),
			name: player.game_name.clone(),
			team: player.team_id.to_string(),
			tag: player.tag_line.clone(),
		}
	} else {
		MatchesV4DataRoundPlayer {
			puuid: puuid.to_string(),
			name: "Unknown".to_string(),
			tag: "Unknown".to_string(),
			team: "Unknown".to_string(),
		}
	}
}

pub fn parse_locations(players: &Vec<Player>, locations: &Vec<PlayerLocation>) -> Vec<MatchesV2DataRoundPlayerLocationsOnEvent> {
	locations
		.iter()
		.map(|i| {
			let player = players.iter().find(|j| j.subject.to_string() == i.subject.to_string());
			if let Some(player) = player {
				MatchesV2DataRoundPlayerLocationsOnEvent {
					player_puuid: player.subject.to_string(),
					player_display_name: format!("{}#{}", player.game_name, player.tag_line),
					player_team: player.team_id.to_string(),
					location: MatchesV2DataRoundEventLocation {
						x: i.location.x,
						y: i.location.y,
					},
					view_radians: i.view_radians,
				}
			} else {
				MatchesV2DataRoundPlayerLocationsOnEvent {
					player_puuid: i.subject.to_string(),
					player_display_name: "Unknown".to_string(),
					player_team: "Unknown".to_string(),
					location: MatchesV2DataRoundEventLocation {
						x: i.location.x,
						y: i.location.y,
					},
					view_radians: i.view_radians,
				}
			}
		})
		.collect::<Vec<MatchesV2DataRoundPlayerLocationsOnEvent>>()
}

pub fn parse_locations_v4(players: &Vec<Player>, locations: &Vec<PlayerLocation>) -> Vec<MatchesV4DataRoundPlayerLocations> {
	locations
		.iter()
		.map(|i| {
			let player = players.iter().find(|j| j.subject.to_string() == i.subject.to_string());
			if let Some(player) = player {
				MatchesV4DataRoundPlayerLocations {
					player: MatchesV4DataRoundPlayer {
						puuid: player.subject.to_string(),
						name: player.game_name.clone(),
						tag: player.tag_line.clone(),
						team: player.team_id.to_string(),
					},
					location: MatchesV4DataRoundLocation {
						x: i.location.x,
						y: i.location.y,
					},
					view_radians: i.view_radians,
				}
			} else {
				MatchesV4DataRoundPlayerLocations {
					player: MatchesV4DataRoundPlayer {
						puuid: i.subject.to_string(),
						name: "Unknown".to_string(),
						tag: "Unknown".to_string(),
						team: "Unknown".to_string(),
					},
					location: MatchesV4DataRoundLocation {
						x: i.location.x,
						y: i.location.y,
					},
					view_radians: i.view_radians,
				}
			}
		})
		.collect::<Vec<MatchesV4DataRoundPlayerLocations>>()
}

pub async fn parse_player_stats(
	stats: &Vec<PlayerRoundStats>,
	players: &Vec<Player>,
	parsed_players: &mut Vec<MatchesV2DataPlayer>
) -> Vec<MatchesV2DataRoundPlayerStats> {
	let mut stats_ = vec![];
	for i in stats {
		let mut damage: u32 = 0;
		let mut bodyshots: u32 = 0;
		let mut headshots: u32 = 0;
		let mut legshots: u32 = 0;
		let mut damage_stats: Vec<MatchesV2DataRoundPlayerStatsDamageEvents> = vec![];
		let mut kills: Vec<MatchesV2DataRoundPlayerStatsKillEvents> = vec![];

		let weapons = get_weapons().await;
		let gears = get_gears().await;
		let parsed_player = parse_round_player(players, &i.subject.to_string());

		for k in i.damage.iter() {
			let receiver = parse_round_player(players, &k.receiver.to_string());
			if k.damage != 999 {
				damage += k.damage;
				parsed_players
					.iter_mut()
					.find(|j| j.puuid == k.receiver.to_string())
					.unwrap().damage_received += k.damage as i32;
			}
			bodyshots += k.bodyshots;
			headshots += k.headshots;
			legshots += k.legshots;
			damage_stats.push(MatchesV2DataRoundPlayerStatsDamageEvents {
				receiver_puuid: receiver.puuid,
				receiver_display_name: receiver.display_name,
				damage: k.damage,
				bodyshots: k.bodyshots,
				headshots: k.headshots,
				legshots: k.legshots,
				receiver_team: receiver.team,
			});
		}

		for k in &i.kills {
			let victim = parse_round_player(players, &k.victim.to_string());
			let killer = parse_round_player(players, &k.killer.unwrap_or(Uuid::new_v4()).to_string());
			let locations = parse_locations(players, &k.player_locations);
			let assistants: Vec<MatchesV2DataRoundPlayerStatsKillEventsAssistants> = k.assistants
				.iter()
				.map(|j| {
					let assistant = parse_round_player(players, &j.to_string());
					MatchesV2DataRoundPlayerStatsKillEventsAssistants {
						assistant_puuid: assistant.puuid,
						assistant_display_name: assistant.display_name,
						assistant_team: assistant.team,
					}
				})
				.collect::<Vec<MatchesV2DataRoundPlayerStatsKillEventsAssistants>>();
			let weapon = if k.finishing_damage.damage_type.clone().is_some() && k.finishing_damage.damage_type.clone().unwrap() == KillFinishingDamageType::Weapon {
				let weapon = weapons.iter().find(|i| { i.uuid.to_string() == k.finishing_damage.damage_item.clone().unwrap().to_string() });
				if let Some(weapon) = weapon {
					Some(weapon)
				} else {
					None
				}
			} else {
				None
			};

			kills.push(MatchesV2DataRoundPlayerStatsKillEvents {
				kill_time_in_round: k.round_time,
				kill_time_in_match: k.game_time,
				killer_puuid: killer.puuid,
				killer_display_name: killer.display_name,
				killer_team: killer.team,
				victim_puuid: victim.puuid,
				victim_display_name: victim.display_name,
				victim_team: victim.team,
				victim_death_location: MatchesV2DataRoundEventLocation {
					x: k.victim_location.x,
					y: k.victim_location.y,
				},
				damage_weapon_id: if weapon.is_some() {
					weapon.unwrap().uuid.to_string()
				} else {
					"".to_string()
				},
				damage_weapon_name: if weapon.is_some() {
					Some(weapon.unwrap().display_name.clone())
				} else {
					None
				},
				damage_weapon_assets: MatchesV2DataRoundPlayerStatsKillEventsAssets {
					display_icon: if weapon.is_some() {
						Some(weapon.unwrap().display_icon.clone().unwrap())
					} else {
						None
					},
					killfeed_icon: if weapon.is_some() {
						Some(weapon.unwrap().kill_stream_icon.clone())
					} else {
						None
					},
				},
				secondary_fire_mode: false,
				assistants,
				player_locations_on_kill: locations,
			});
		}

		let mut_player = parsed_players
			.iter_mut()
			.find(|j| j.puuid == i.subject.to_string())
			.unwrap();
		mut_player.damage_made += damage as i32;
		mut_player.stats.bodyshots += bodyshots;
		mut_player.stats.headshots += headshots;
		mut_player.stats.legshots += legshots;
		mut_player.economy.spent.overall += i.economy.spent;
		mut_player.economy.loadout_value.overall += i.economy.loadout_value as i32;

		stats_.push(MatchesV2DataRoundPlayerStats {
			ability_casts: MatchesV2DataRoundPlayerStatsAbilityCasts {
				x_casts: None,
				e_casts: None,
				q_casts: None,
				c_casts: None,
			},
			player_puuid: parsed_player.puuid,
			player_display_name: parsed_player.display_name,
			player_team: parsed_player.team,
			kills: i.kills.len() as u32,
			score: i.score,
			damage,
			headshots,
			bodyshots,
			legshots,
			economy: MatchesV2DataRoundPlayerStatsEconomy {
				loadout_value: i.economy.loadout_value,
				remaining: i.economy.remaining,
				spent: i.economy.spent,
				weapon: MatchesV2DataRoundPlayerStatsEconomyEquipmentWeapon {
					id: {
						if let Some(id) = i.economy.weapon { Some(id.to_string()) } else { None }
					},
					name: {
						if let Some(id) = i.economy.weapon {
							let weapon = weapons.iter().find(|i| i.uuid.to_string() == id.to_string());
							if let Some(weapon) = weapon {
								Some(weapon.display_name.clone())
							} else {
								None
							}
						} else {
							None
						}
					},
					assets: MatchesV2DataRoundPlayerStatsEconomyEquipmentAssets {
						display_icon: {
							if let Some(id) = i.economy.weapon {
								let weapon = weapons.iter().find(|i| i.uuid.to_string() == id.to_string());
								if let Some(weapon) = weapon {
									Some(weapon.display_icon.clone().unwrap())
								} else {
									None
								}
							} else {
								None
							}
						},
						killfeed_icon: {
							if let Some(id) = i.economy.weapon {
								let weapon = weapons.iter().find(|i| i.uuid.to_string() == id.to_string());
								if let Some(weapon) = weapon {
									Some(weapon.kill_stream_icon.clone())
								} else {
									None
								}
							} else {
								None
							}
						},
					},
				},
				armor: MatchesV2DataRoundPlayerStatsEconomyEquipmentArmor {
					id: {
						if let Some(id) = i.economy.armor { Some(id.to_string()) } else { None }
					},
					name: {
						if let Some(id) = i.economy.armor {
							let gear = gears.iter().find(|i| i.uuid == id);
							if let Some(gear) = gear {
								Some(gear.display_name.clone())
							} else {
								None
							}
						} else {
							None
						}
					},
					assets: MatchesV2DataRoundPlayerStatsEconomyEquipmentAssetsArmor {
						display_icon: {
							if let Some(id) = i.economy.armor {
								let gear = gears.iter().find(|i| i.uuid == id);
								if let Some(gear) = gear {
									Some(gear.display_icon.clone().unwrap())
								} else {
									None
								}
							} else {
								None
							}
						},
					},
				},
			},
			was_afk: i.was_afk,
			was_penalized: i.was_penalized,
			damage_events: damage_stats,
			kill_events: kills,
			stayed_in_spawn: i.stayed_in_spawn,
		});
	}
	stats_
}

pub async fn parse_player_stats_v4(
	stats: &Vec<PlayerRoundStats>,
	players: &Vec<Player>,
	parsed_players: &mut Vec<MatchesV4DataPlayer>
) -> Vec<MatchesV4DataRoundPlayerStats> {
	let mut stats_ = vec![];
	for i in stats {
		let mut damage: u32 = 0;
		let mut bodyshots: u32 = 0;
		let mut headshots: u32 = 0;
		let mut legshots: u32 = 0;
		let mut damage_stats: Vec<MatchesV4DataRoundPlayerStatsDamageEvents> = vec![];

		let weapons = get_weapons().await;
		let gears = get_gears().await;
		let parsed_player = parse_round_player_v4(players, &i.subject.to_string());

		for k in i.damage.iter() {
			let receiver = parse_round_player_v4(players, &k.receiver.to_string());
			if k.damage != 999 {
				damage += k.damage;
				parsed_players
					.iter_mut()
					.find(|j| j.puuid == k.receiver.to_string())
					.unwrap().stats.damage.received += k.damage as i32;
			}
			bodyshots += k.bodyshots;
			headshots += k.headshots;
			legshots += k.legshots;
			damage_stats.push(MatchesV4DataRoundPlayerStatsDamageEvents {
				player: MatchesV4DataRoundPlayer {
					puuid: receiver.puuid,
					name: receiver.name,
					tag: receiver.tag,
					team: receiver.team,
				},
				damage: k.damage,
				bodyshots: k.bodyshots,
				headshots: k.headshots,
				legshots: k.legshots,
			});
		}

		let mut_player = parsed_players
			.iter_mut()
			.find(|j| j.puuid == i.subject.to_string())
			.unwrap();
		mut_player.stats.damage.dealt += damage as i32;
		mut_player.stats.bodyshots += bodyshots;
		mut_player.stats.headshots += headshots;
		mut_player.stats.legshots += legshots;
		mut_player.economy.spent.overall += i.economy.spent;
		mut_player.economy.loadout_value.overall += i.economy.loadout_value as i32;

		stats_.push(MatchesV4DataRoundPlayerStats {
			player: parsed_player,
			ability_casts: MatchesV4DataRoundPlayerStatsAbilityCasts {
				ultimate: None,
				ability_2: None,
				ability_1: None,
				grenade: None,
			},
			damage_events: damage_stats,
			stats: MatchesV4DataRoundPlayerStatsStats {
				score: i.score,
				kills: i.kills.len() as u32,
				headshots,
				bodyshots,
				legshots,
			},
			economy: MatchesV4DataRoundPlayerStatsEconomy {
				loadout_value: i.economy.loadout_value as i32,
				remaining: i.economy.remaining as i32,
				weapon: if let Some(weapon) = i.economy.weapon {
					let weapon = weapons.iter().find(|i| i.uuid.to_string() == weapon.to_string());
					Some(MatchesV4DataRoundPlayerStatsEconomyWeapon {
						id: if let Some(weapon) = i.economy.weapon {
							Some(weapon.to_string())
						} else {
							None
						},
						name: if let Some(weapon) = weapon {
							Some(weapon.display_name.clone())
						} else {
							None
						},
						type_: Some("Weapon".to_string()),
					})
				} else {
					None
				},
				armor: if let Some(armor) = i.economy.armor {
					let armor = gears.iter().find(|i| i.uuid == armor);
					Some(MatchesV4DataRoundPlayerStatsEconomyArmor {
						id: if let Some(armor) = i.economy.armor {
							armor.to_string()
						} else {
							"".to_string()
						},
						name: if let Some(armor) = armor {
							Some(armor.display_name.clone())
						} else {
							None
						},
					})
				} else {
					None
				},
			},
			was_afk: i.was_afk,
			received_penalty: i.was_penalized,
			stayed_in_spawn: i.stayed_in_spawn,
		});
	}
	stats_
}

fn parse_mmr_images(tier: &i32) -> MMRDataImages {
	MMRDataImages {
		small: format!("https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/{}/smallicon.png", tier),
		large: format!("https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/{}/largeicon.png", tier),
		triangle_down: format!("https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/{}/ranktriangledownicon.png", tier),
		triangle_up: format!("https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/{}/ranktriangleupicon.png", tier),
	}
}

pub fn parse_premier_team_history(team: PremierTeamDB) -> PremierTeamHistoryV1Response {
	PremierTeamHistoryV1Response {
		status: 200,
		data: PremierTeamHistoryV1ResponseData {
			league_matches: team.games.league
				.iter()
				.map(|i| PremierTeamGamesLeagueString {
					id: i.id.clone(),
					points_before: i.points_before,
					points_after: i.points_after,
					started_at: i.started_at.try_to_rfc3339_string().unwrap().clone(),
				})
				.collect(),
			tournament_matches: team.games.tournament,
		},
	}
}

pub fn parse_premier_team(team: PremierTeamDB) -> PremierTeamV1Response {
	PremierTeamV1Response {
		status: 200,
		data: PremierTeamV1ResponseData {
			id: team.id,
			name: team.name,
			tag: team.tag,
			enrolled: true,
			stats: PremierTeamV1ResponseDataStats {
				wins: team.stats.wins,
				losses: team.stats.losses,
				matches: team.stats.losses + team.stats.wins,
				rounds_won: team.stats.rounds_won,
				rounds_lost: team.stats.rounds_lost,
			},
			placement: PremierTeamV1ResponseDataPlacement {
				points: team.stats.score,
				conference: team.conference,
				division: team.division,
				place: team.stats.ranking,
			},
			customization: PremierTeamV1ResponseDataCustomization {
				icon: team.customization.icon.clone(),
				image: format!(
					"https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/{}?primary={}&secondary={}&tertiary={}",
					team.customization.icon.clone(),
					team.customization.primary.clone(),
					team.customization.secondary.clone(),
					team.customization.tertiary.clone()
				),
				primary: format!("#{}", team.customization.primary),
				secondary: format!("#{}", team.customization.secondary),
				tertiary: format!("#{}", team.customization.tertiary),
			},
			member: team.member,
		},
	}
}

pub fn parse_premier_team_lite(teams: Vec<PremierTeamDB>) -> Vec<PremierTeamLiteResponseData> {
	teams
		.iter()
		.map(|team| PremierTeamLiteResponseData {
			id: team.id.clone(),
			name: team.name.clone(),
			tag: team.tag.clone(),
			conference: team.conference.clone(),
			division: team.division,
			region: team.region.clone(),
			affinity: team.affinity.clone(),
			losses: team.stats.losses,
			wins: team.stats.wins,
			score: team.stats.score,
			ranking: team.stats.ranking,
			customization: PremierTeamV1ResponseDataCustomization {
				icon: team.customization.icon.clone(),
				image: format!(
					"https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/{}?primary={}&secondary={}&tertiary={}",
					team.customization.icon.clone(),
					team.customization.primary.clone(),
					team.customization.secondary.clone(),
					team.customization.tertiary.clone()
				),
				primary: format!("#{}", team.customization.primary),
				secondary: format!("#{}", team.customization.secondary),
				tertiary: format!("#{}", team.customization.tertiary),
			},
			updated_at: team.updated_at.try_to_rfc3339_string().unwrap().clone(),
		})
		.collect::<Vec<PremierTeamLiteResponseData>>()
}

pub fn parse_v3_leaderboard(pagination: Pagination, meta: LeaderboardMetadataDB, player: Vec<LeaderboardPlayerDB>) -> LeaderboardV3Response {
	let mut thresholds = meta.thresholds
		.iter()
		.map(|i| LeaderboardV3DataThreshold {
			tier: {
				let id = i.0.parse::<i32>().unwrap();
				let old = meta.thresholds.iter().any(|j| j.0 == "21");
				LeaderboardV3DataThresholdTier {
					id,
					name: if old {
						get_old_tier(id)
					} else {
						get_tier(id)
					},
				}
			},
			start_index: i.1.starting_index,
			threshold: i.1.ranked_rating_threshold as i32,
		})
		.collect::<Vec<LeaderboardV3DataThreshold>>();
	thresholds.sort_by(|a, b| a.tier.id.cmp(&b.tier.id));
	let players = player
		.into_iter()
		.map(|i| LeaderboardV3DataPlayer {
			card: i.player.player_card_id,
			title: i.player.title_id,
			is_banned: i.player.is_banned,
			is_anonymized: i.player.is_anonymized,
			puuid: i.player.puuid,
			name: i.player.game_name,
			tag: i.player.tag_line,
			leaderboard_rank: i.player.leaderboard_rank as i32,
			tier: i.player.competitive_tier as i32,
			rr: i.player.ranked_rating as i32,
			wins: i.player.number_of_wins as i32,
			updated_at: i.updated_at.try_to_rfc3339_string().unwrap(),
		})
		.collect::<Vec<LeaderboardV3DataPlayer>>();
	LeaderboardV3Response {
		status: 200,
		results: pagination,
		data: LeaderboardV3Data {
			updated_at: meta.updated_at.try_to_rfc3339_string().unwrap(),
			thresholds,
			players,
		},
	}
}

pub fn parse_v3_leaderboard_from_api(pagination: Pagination, meta: LeaderboardMetadataDB, player: Vec<LeaderboardPlayer>) -> LeaderboardV3Response {
	let mut thresholds = meta.thresholds
		.iter()
		.map(|i| LeaderboardV3DataThreshold {
			tier: {
				let id = i.0.parse::<i32>().unwrap();
				let old = meta.thresholds.iter().any(|j| j.0 == "21");
				LeaderboardV3DataThresholdTier {
					id,
					name: if old {
						get_old_tier(id)
					} else {
						get_tier(id)
					},
				}
			},
			start_index: i.1.starting_index,
			threshold: i.1.ranked_rating_threshold as i32,
		})
		.collect::<Vec<LeaderboardV3DataThreshold>>();
	thresholds.sort_by(|a, b| a.tier.id.cmp(&b.tier.id));
	let players = player
		.into_iter()
		.map(|i| LeaderboardV3DataPlayer {
			card: i.player_card_id.to_string(),
			title: i.title_id.to_string(),
			is_banned: i.is_banned,
			is_anonymized: i.is_anonymized,
			puuid: if i.puuid.is_some() {
				Some(i.puuid.unwrap().to_string())
			} else {
				None
			},
			name: i.game_name,
			tag: i.tag_line,
			leaderboard_rank: i.leaderboard_rank as i32,
			tier: i.competitive_tier as i32,
			rr: i.ranked_rating as i32,
			wins: i.number_of_wins as i32,
			updated_at: DateTime::now().try_to_rfc3339_string().unwrap(),
		})
		.collect::<Vec<LeaderboardV3DataPlayer>>();
	LeaderboardV3Response {
		status: 200,
		results: pagination,
		data: LeaderboardV3Data {
			updated_at: meta.updated_at.try_to_rfc3339_string().unwrap(),
			thresholds,
			players,
		},
	}
}
