use crate::structs::database::{ LeaderboardPVPPlayer, PremierTeamGamesLeagueString, PremierTeamGamesTournament, PremierTeamMember };
use crate::structs::pvp_api::StatusIncident;
use mongodb::{ bson::serde_helpers::serialize_bson_datetime_as_rfc3339_string, bson::DateTime };
use serde::{ Deserialize, Serialize };
use serde_json::Value;
use std::collections::HashMap;
use valorant_api::response_types::store_front_v2::FeaturedBundle;
use utoipa::ToSchema;
use valorant_api::response_types::leaderboard_v1::LeaderboardPlayer;

//Utils
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct SeasonIdShortCombo {
	pub id: String,
	pub short: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct TierIdNameCombo {
	pub id: i32,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone, ToSchema)]
pub struct TierIdNameRRCombo {
	pub id: i32,
	pub name: String,
	pub rr: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MapIdNameCombo {
	pub id: String,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct AgentIdNameCombo {
	pub id: String,
	pub name: String,
}

//pagination
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct Pagination {
	pub total: i32,
	pub returned: i32,
	pub before: i32,
	pub after: i32,
}

//Account
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct AccountV1Response {
	pub status: u32,
	pub data: AccountV1Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct AccountV1Data {
	pub puuid: String,
	pub region: String,
	pub account_level: i32,
	pub name: String,
	pub tag: String,
	pub card: AccountV1DataCard,
	pub last_update: String,
	pub last_update_raw: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct AccountV1DataCard {
	pub small: String,
	pub large: String,
	pub wide: String,
	pub id: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct AccountV2Response {
	pub status: u32,
	pub data: AccountV2Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct AccountV2Data {
	pub puuid: String,
	pub region: String,
	pub account_level: i32,
	pub name: String,
	pub tag: String,
	pub card: String,
	pub title: String,
	pub platforms: Vec<String>,
	pub updated_at: String,
}

//Esports
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1Response {
	pub status: u32,
	pub data: Vec<EsportsV1Data>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1Data {
	pub date: String,
	pub state: String,
	#[serde(rename = "type")]
	pub type_: String,
	pub vod: Option<String>,
	pub league: EsportsV1DataLeague,
	pub tournament: EsportsV1DataTournament,
	#[serde(rename = "match")]
	pub match_: EsportsV1DataMatch,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1DataLeague {
	pub name: String,
	pub identifier: String,
	pub icon: String,
	pub region: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1DataTournament {
	pub name: String,
	pub season: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1DataMatch {
	pub id: Option<String>,
	pub teams: Vec<EsportsV1DataMatchTeams>,
	pub game_type: EsportsV1DataMatchGameType,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1DataMatchTeams {
	pub name: String,
	pub code: String,
	pub icon: String,
	pub has_won: bool,
	pub game_wins: i32,
	pub record: EsportsV1DataMatchTeamsRecord,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1DataMatchTeamsRecord {
	pub wins: i32,
	pub losses: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct EsportsV1DataMatchGameType {
	#[serde(rename = "type")]
	pub type_: Option<String>,
	pub count: Option<i32>,
}

//Leaderboard
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV1Response {
	pub status: u32,
	pub data: Vec<LeaderboardPVPPlayer>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LeaderboardV1DirectFetchResponse {
	pub status: u32,
	pub data: Vec<LeaderboardPlayer>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV2Response {
	pub last_update: i64,
	pub next_update: i64,
	pub total_players: i32,
	pub radiant_threshold: i32,
	pub immortal_3_threshold: i32,
	pub immortal_2_threshold: i32,
	pub immortal_1_threshold: i32,
	pub players: Vec<LeaderboardPVPPlayer>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV3Response {
	pub status: u32,
	pub results: Pagination,
	pub data: LeaderboardV3Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV3Data {
	pub updated_at: String,
	pub thresholds: Vec<LeaderboardV3DataThreshold>,
	pub players: Vec<LeaderboardV3DataPlayer>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV3DataThreshold {
	pub tier: LeaderboardV3DataThresholdTier,
	pub start_index: i32,
	pub threshold: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV3DataThresholdTier {
	pub id: i32,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct LeaderboardV3DataPlayer {
	pub card: String,
	pub title: String,
	pub is_banned: bool,
	pub is_anonymized: bool,
	pub puuid: Option<String>,
	pub name: String,
	pub tag: String,
	pub leaderboard_rank: i32,
	pub tier: i32,
	pub rr: i32,
	pub wins: i32,
	pub updated_at: String,
}

//Stored MMR
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMRV2Response {
	pub status: u32,
	pub results: Pagination,
	pub data: Vec<StoredMMRV2>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMRV2 {
	pub tier: TierIdNameCombo,
	pub match_id: String,
	pub map: MapIdNameCombo,
	pub season: SeasonIdShortCombo,
	pub rr: i32,
	pub last_change: i32,
	pub elo: i32,
	pub refunded_rr: i32,
	pub was_derank_protected: bool,
	pub date: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMRResponse {
	pub status: u32,
	pub results: Pagination,
	pub data: Vec<StoredMMR>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMR {
	pub match_id: String,
	pub tier: StoredMMRTier,
	pub map: StoredMMRMap,
	pub season: StoredMMRSeason,
	pub ranking_in_tier: u32,
	pub last_mmr_change: i32,
	pub elo: i32,
	pub date: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMRTier {
	pub id: i32,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMRMap {
	pub id: String,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMMRSeason {
	pub id: String,
	pub short: String,
}

//Stored Matches
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchesResponse {
	pub status: u32,
	pub results: Pagination,
	pub data: Vec<StoredMatch>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatch {
	pub meta: StoredMatchMeta,
	pub stats: StoredMatchStats,
	pub teams: StoredMatchTeam,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchTeam {
	pub red: u8,
	pub blue: u8,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchStats {
	pub puuid: String,
	pub team: String,
	pub level: u32,
	pub character: StoredMatchStatsCharacter,
	pub tier: u32,
	pub score: i32,
	pub kills: u32,
	pub deaths: u32,
	pub assists: u32,
	pub shots: StoredMatchStatsShots,
	pub damage: StoredMatchStatsDamage,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchStatsDamage {
	pub made: i32,
	pub received: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchStatsCharacter {
	pub id: String,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchStatsShots {
	pub head: u32,
	pub body: u32,
	pub leg: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchMeta {
	pub id: String,
	pub map: StoredMatchMetaMap,
	pub version: String,
	pub mode: String,
	pub started_at: String,
	pub season: StoredMatchMetaSeason,
	pub region: String,
	pub cluster: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchMetaMap {
	pub id: String,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoredMatchMetaSeason {
	pub id: String,
	pub short: String,
}

//MMR
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3Response {
	pub status: u32,
	pub data: MMRV3Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3Data {
	pub account: MMRV3Account,
	pub peak: Option<MMRV3Peak>,
	pub current: MMRV3Current,
	pub seasonal: Vec<MMRV3Seasonal>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3Account {
	pub name: String,
	pub tag: String,
	pub puuid: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3Peak {
	pub season: SeasonIdShortCombo,
	pub ranking_schema: String,
	pub tier: TierIdNameCombo,
	pub rr: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3Current {
	pub tier: TierIdNameCombo,
	pub rr: i32,
	pub last_change: i32,
	pub elo: i32,
	pub games_needed_for_rating: i32,
	pub rank_protection_shields: i32,
	pub leaderboard_placement: Option<MMRV3LeaderboardPlacement>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3LeaderboardPlacement {
	pub rank: u32,
	pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV3Seasonal {
	pub season: SeasonIdShortCombo,
	pub wins: i32,
	pub games: i32,
	pub end_tier: TierIdNameCombo,
	pub end_rr: i32,
	pub ranking_schema: String,
	pub leaderboard_placement: Option<MMRV3LeaderboardPlacement>,
	pub act_wins: Vec<TierIdNameCombo>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV2Response {
	pub status: u32,
	pub data: MMRV2Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV2Data {
	pub name: String,
	pub tag: String,
	pub puuid: String,
	pub current_data: MMRV2CurrentData,
	pub highest_rank: MMRV2HighestRank,
	pub by_season: Value,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV2BySeason {
	pub error: bool,
	pub wins: i32,
	pub number_of_games: i32,
	pub final_rank: i32,
	pub final_rank_patched: String,
	pub act_rank_wins: Vec<MMRV2BySeasonActRankWins>,
	pub old: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV2BySeasonActRankWins {
	pub patched_tier: String,
	pub tier: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV2HighestRank {
	pub old: bool,
	pub tier: i32,
	pub patched_tier: String,
	pub season: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV2CurrentData {
	pub currenttier: i32,
	pub currenttierpatched: String,
	pub images: MMRDataImages,
	pub ranking_in_tier: i32,
	pub mmr_change_to_last_game: i32,
	pub elo: i32,
	pub games_needed_for_rating: i32,
	pub old: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV1Response {
	pub status: u32,
	pub data: MMRV1Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRV1Data {
	pub currenttier: i32,
	pub currenttierpatched: String,
	pub images: MMRDataImages,
	pub ranking_in_tier: i32,
	pub mmr_change_to_last_game: i32,
	pub elo: i32,
	pub name: String,
	pub tag: String,
	pub old: bool,
}

//MMR History
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRHistoryV2Response {
	pub status: u32,
	pub data: MMRHistoryV2Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRHistoryV2Data {
	pub account: MMRV3Account,
	pub history: Vec<MMRHistoryV2History>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRHistoryV2History {
	pub tier: TierIdNameCombo,
	pub match_id: String,
	pub map: MapIdNameCombo,
	pub season: SeasonIdShortCombo,
	pub rr: i32,
	pub last_change: i32,
	pub elo: i32,
	pub refunded_rr: i32,
	pub was_derank_protected: bool,
	pub date: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRHistoryV1Response {
	pub status: u32,
	pub name: String,
	pub tag: String,
	pub data: Vec<MMRHistoryV1Data>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRHistoryV1Data {
	pub currenttier: i32,
	pub currenttierpatched: String,
	pub images: MMRDataImages,
	pub match_id: String,
	pub map: MMRHistoryV1DataMap,
	pub season_id: String,
	pub ranking_in_tier: i32,
	pub mmr_change_to_last_game: i32,
	pub elo: i32,
	pub date: String,
	pub date_raw: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRHistoryV1DataMap {
	pub id: String,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MMRDataImages {
	pub small: String,
	pub large: String,
	pub triangle_down: String,
	pub triangle_up: String,
}

//Matches

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4HistoryResponse {
	pub status: u32,
	pub data: Vec<MatchesV4Data>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4Response {
	pub status: u32,
	pub data: MatchesV4Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4Data {
	pub metadata: MatchesV4DataMetadata,
	pub players: Vec<MatchesV4DataPlayer>,
	pub observers: Vec<MatchesV4DataObserver>,
	pub coaches: Vec<MatchesV4DataCoach>,
	pub teams: Vec<MatchesV4DataTeam>,
	pub rounds: Vec<MatchesV4DataRound>,
	pub kills: Vec<MatchesV4DataKill>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataKill {
	pub time_in_round_in_ms: u64,
	pub time_in_match_in_ms: u64,
	pub round: u8,
	pub killer: MatchesV4DataRoundPlayer,
	pub victim: MatchesV4DataRoundPlayer,
	pub assistants: Vec<MatchesV4DataRoundPlayer>,
	pub location: MatchesV4DataRoundLocation,
	pub weapon: MatchesV4DataRoundPlayerStatsEconomyWeapon,
	pub secondary_fire_mode: bool,
	pub player_locations: Vec<MatchesV4DataRoundPlayerLocations>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRound {
	pub id: u8,
	pub result: String,
	pub ceremony: String,
	pub winning_team: String,
	pub plant: Option<MatchesV4DataRoundPlant>,
	pub defuse: Option<MatchesV4DataRoundDefuse>,
	pub stats: Vec<MatchesV4DataRoundPlayerStats>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStats {
	pub player: MatchesV4DataRoundPlayer,
	pub ability_casts: MatchesV4DataRoundPlayerStatsAbilityCasts,
	pub damage_events: Vec<MatchesV4DataRoundPlayerStatsDamageEvents>,
	pub stats: MatchesV4DataRoundPlayerStatsStats,
	pub economy: MatchesV4DataRoundPlayerStatsEconomy,
	pub was_afk: bool,
	pub received_penalty: bool,
	pub stayed_in_spawn: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStatsDamageEvents {
	pub player: MatchesV4DataRoundPlayer,
	pub bodyshots: u32,
	pub headshots: u32,
	pub legshots: u32,
	pub damage: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStatsAbilityCasts {
	pub grenade: Option<u32>,
	pub ability_1: Option<u32>,
	pub ability_2: Option<u32>,
	pub ultimate: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStatsEconomy {
	pub loadout_value: i32,
	pub remaining: i32,
	pub weapon: Option<MatchesV4DataRoundPlayerStatsEconomyWeapon>,
	pub armor: Option<MatchesV4DataRoundPlayerStatsEconomyArmor>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStatsEconomyArmor {
	pub id: String,
	pub name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStatsEconomyWeapon {
	pub id: Option<String>,
	pub name: Option<String>,
	#[serde(rename = "type")]
	pub type_: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerStatsStats {
	pub score: i32,
	pub kills: u32,
	pub headshots: u32,
	pub bodyshots: u32,
	pub legshots: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlant {
	pub round_time_in_ms: i32,
	pub site: String,
	pub location: MatchesV4DataRoundLocation,
	pub player: MatchesV4DataRoundPlayer,
	pub player_locations: Vec<MatchesV4DataRoundPlayerLocations>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundDefuse {
	pub round_time_in_ms: i32,
	pub location: MatchesV4DataRoundLocation,
	pub player: MatchesV4DataRoundPlayer,
	pub player_locations: Vec<MatchesV4DataRoundPlayerLocations>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayerLocations {
	pub player: MatchesV4DataRoundPlayer,
	pub view_radians: f32,
	pub location: MatchesV4DataRoundLocation,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundLocation {
	pub x: i32,
	pub y: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataRoundPlayer {
	pub puuid: String,
	pub name: String,
	pub tag: String,
	pub team: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataTeam {
	pub team_id: String,
	pub rounds: MatchesV4DataTeamRounds,
	pub won: bool,
	pub premier_roster: Option<MatchesV4DataTeamPremierRoster>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataTeamRounds {
	pub won: u8,
	pub lost: u8,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataTeamPremierRoster {
	pub id: String,
	pub name: String,
	pub tag: String,
	pub members: Vec<String>,
	pub customization: MatchesV4DataTeamPremierRosterCustomization,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataTeamPremierRosterCustomization {
	pub icon: String,
	pub image: String,
	pub primary_color: String,
	pub secondary_color: String,
	pub tertiary_color: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataCoach {
	pub puuid: String,
	pub team_id: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataObserver {
	pub puuid: String,
	pub name: String,
	pub tag: String,
	pub account_level: u32,
	pub session_playtime_in_ms: u32,
	pub card_id: String,
	pub title_id: String,
	pub party_id: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayer {
	pub puuid: String,
	pub name: String,
	pub tag: String,
	pub team_id: String,
	pub platform: String,
	pub party_id: String,
	pub agent: AgentIdNameCombo,
	pub stats: MatchesV4DataPlayerStats,
	pub ability_casts: MatchesV4DataPlayerAbilityCasts,
	pub tier: TierIdNameCombo,
	pub customization: MatchesV4DataPlayerCustomization,
	pub account_level: u32,
	pub session_playtime_in_ms: u32,
	pub behavior: MatchesV4DataPlayerBehavior,
	pub economy: MatchesV4DataPlayerEconomy,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerCustomization {
	pub card: String,
	pub title: String,
	pub preferred_level_border: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerBehavior {
	pub afk_rounds: f32,
	pub friendly_fire: MatchesV4DataPlayerBehaviorFriendlyFire,
	pub rounds_in_spawn: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerBehaviorFriendlyFire {
	pub incoming: f32,
	pub outgoing: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerEconomy {
	pub spent: MatchesV4DataPlayerEconomySpent,
	pub loadout_value: MatchesV4DataPlayerEconomyLoadoutValue,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerEconomySpent {
	pub overall: i32,
	pub average: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerEconomyLoadoutValue {
	pub overall: i32,
	pub average: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerStats {
	pub score: i32,
	pub kills: u32,
	pub deaths: u32,
	pub assists: u32,
	pub headshots: u32,
	pub bodyshots: u32,
	pub legshots: u32,
	pub damage: MatchesV4DataPlayerStatsDamage,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerStatsDamage {
	pub dealt: i32,
	pub received: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataPlayerAbilityCasts {
	pub grenade: Option<u32>,
	pub ability1: Option<u32>,
	pub ability2: Option<u32>,
	pub ultimate: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataMetadata {
	pub match_id: String,
	pub map: MapIdNameCombo,
	pub game_version: String,
	pub game_length_in_ms: u64,
	pub started_at: String,
	pub is_completed: bool,
	pub queue: MatchesV4DataMetadataQueue,
	pub season: SeasonIdShortCombo,
	pub platform: String,
	pub premier: Option<()>,
	pub party_rr_penaltys: Vec<MatchesV4DataMetadataPartyRRPenalty>,
	pub region: Option<String>,
	pub cluster: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataMetadataQueue {
	pub id: String,
	pub name: Option<String>,
	pub mode_type: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV4DataMetadataPartyRRPenalty {
	pub party_id: String,
	pub penalty: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV3ListResponse {
	pub status: u32,
	pub data: Vec<MatchesV3ListResponseData>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2Response {
	pub status: u32,
	pub data: MatchesV2Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV3ListResponseData {
	pub is_available: bool,
	pub metadata: Option<MatchesV2DataMetadata>,
	pub players: Option<MatchesV2DataPlayers>,
	pub observers: Vec<MatchesV2DataObserver>,
	pub coaches: Vec<MatchesV2DataCoach>,
	pub teams: Option<MatchesV2DataTeams>,
	pub rounds: Vec<MatchesV2DataRound>,
	pub kills: Vec<MatchesV2DataKill>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2Data {
	pub metadata: MatchesV2DataMetadata,
	pub players: MatchesV2DataPlayers,
	pub observers: Vec<MatchesV2DataObserver>,
	pub coaches: Vec<MatchesV2DataCoach>,
	pub teams: MatchesV2DataTeams,
	pub rounds: Vec<MatchesV2DataRound>,
	pub kills: Vec<MatchesV2DataKill>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataMetadata {
	pub map: Option<String>,
	pub game_version: String,
	pub game_length: u64,
	pub game_start: u64,
	pub game_start_patched: String,
	pub rounds_played: u8,
	pub mode: Option<String>,
	pub mode_id: String,
	pub queue: Option<String>,
	pub season_id: String,
	pub platform: String,
	pub matchid: String,
	pub premier_info: MatchesV2DataMetadataPremierInfo,
	pub region: Option<String>,
	pub cluster: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataMetadataPremierInfo {
	pub tournament_id: Option<String>,
	pub matchup_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayers {
	pub all_players: Vec<MatchesV2DataPlayer>,
	pub red: Vec<MatchesV2DataPlayer>,
	pub blue: Vec<MatchesV2DataPlayer>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayer {
	pub puuid: String,
	pub name: String,
	pub tag: String,
	pub team: String,
	pub level: u32,
	pub character: Option<String>,
	pub currenttier: u8,
	pub currenttier_patched: String,
	pub player_card: String,
	pub player_title: String,
	pub party_id: String,
	pub session_playtime: MatchesV2DataPlayerSessionPlaytime,
	pub behavior: MatchesV2DataPlayerBehavior,
	pub platform: MatchesV2DataPlatform,
	pub ability_casts: MatchesV2DataPlayerAbilityCasts,
	pub assets: MatchesV2DataPlayerAssets,
	pub stats: MatchesV2DataPlayerStats,
	pub economy: MatchesV2DataPlayerEconomy,
	pub damage_made: i32,
	pub damage_received: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerEconomy {
	pub spent: MatchesV2DataPlayerEconomyValue,
	pub loadout_value: MatchesV2DataPlayerEconomyValue,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerEconomyValue {
	pub overall: i32,
	pub average: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerStats {
	pub score: i32,
	pub kills: u32,
	pub deaths: u32,
	pub assists: u32,
	pub bodyshots: u32,
	pub headshots: u32,
	pub legshots: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerAssets {
	pub card: MatchesV2DataPlayerAssetsCard,
	pub agent: MatchesV2DataPlayerAssetsAgent,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerAssetsCard {
	pub small: String,
	pub large: String,
	pub wide: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerAssetsAgent {
	pub small: String,
	pub bust: String,
	pub full: String,
	pub killfeed: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerAbilityCasts {
	pub x_cast: Option<u32>,
	pub e_cast: Option<u32>,
	pub q_cast: Option<u32>,
	pub c_cast: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlatform {
	#[serde(rename = "type")]
	pub type_: String,
	pub os: MatchesV2DataPlatformOs,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlatformOs {
	pub name: String,
	pub version: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerSessionPlaytime {
	pub minutes: u32,
	pub seconds: u32,
	pub milliseconds: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerBehavior {
	pub afk_rounds: f32,
	pub friendly_fire: MatchesV2DataPlayerBehaviorFriendlyFire,
	pub rounds_in_spawn: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataPlayerBehaviorFriendlyFire {
	pub incoming: Option<f32>,
	pub outgoing: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataObserver {
	pub puuid: String,
	pub name: String,
	pub tag: String,
	pub platform: MatchesV2DataPlatform,
	pub session_playtime: MatchesV2DataPlayerSessionPlaytime,
	pub team: String,
	pub level: u32,
	pub player_card: String,
	pub player_title: String,
	pub party_id: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataCoach {
	pub puuid: String,
	pub team: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataTeams {
	pub red: MatchesV2DataTeam,
	pub blue: MatchesV2DataTeam,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataTeam {
	pub has_won: Option<bool>,
	pub rounds_won: Option<u8>,
	pub rounds_lost: Option<u8>,
	pub roster: Option<MatchesV2DataTeamRoster>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataTeamRoster {
	pub id: String,
	pub members: Vec<String>,
	pub name: String,
	pub tag: String,
	pub customization: MatchesV2DataTeamRosterCustomization,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataTeamRosterCustomization {
	pub icon: String,
	pub image: String,
	pub primary_color: String,
	pub secondary_color: String,
	pub tertiary_color: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRound {
	pub winning_team: String,
	pub end_type: String,
	pub bomb_planted: bool,
	pub bomb_defused: bool,
	pub plant_events: MatchesV2DataRoundPlantEvents,
	pub defuse_events: MatchesV2DataRoundDefuseEvents,
	pub player_stats: Vec<MatchesV2DataRoundPlayerStats>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataKill {
	pub kill_time_in_round: u64,
	pub kill_time_in_match: u64,
	pub round: u8,
	pub killer_puuid: String,
	pub killer_display_name: String,
	pub killer_team: String,
	pub victim_puuid: String,
	pub victim_display_name: String,
	pub victim_team: String,
	pub victim_death_location: MatchesV2DataRoundEventLocation,
	pub damage_weapon_id: String,
	pub damage_weapon_name: Option<String>,
	pub damage_weapon_assets: MatchesV2DataRoundPlayerStatsKillEventsAssets,
	pub secondary_fire_mode: bool,
	pub player_locations_on_kill: Vec<MatchesV2DataRoundPlayerLocationsOnEvent>,
	pub assistants: Vec<MatchesV2DataRoundPlayerStatsKillEventsAssistants>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStats {
	pub ability_casts: MatchesV2DataRoundPlayerStatsAbilityCasts,
	pub player_puuid: String,
	pub player_display_name: String,
	pub player_team: String,
	pub damage_events: Vec<MatchesV2DataRoundPlayerStatsDamageEvents>,
	pub damage: u32,
	pub headshots: u32,
	pub bodyshots: u32,
	pub legshots: u32,
	pub kill_events: Vec<MatchesV2DataRoundPlayerStatsKillEvents>,
	pub kills: u32,
	pub score: i32,
	pub economy: MatchesV2DataRoundPlayerStatsEconomy,
	pub was_afk: bool,
	pub was_penalized: bool,
	pub stayed_in_spawn: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsEconomy {
	pub loadout_value: u32,
	pub remaining: u32,
	pub spent: i32,
	pub weapon: MatchesV2DataRoundPlayerStatsEconomyEquipmentWeapon,
	pub armor: MatchesV2DataRoundPlayerStatsEconomyEquipmentArmor,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsEconomyEquipmentWeapon {
	pub id: Option<String>,
	pub name: Option<String>,
	pub assets: MatchesV2DataRoundPlayerStatsEconomyEquipmentAssets,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsEconomyEquipmentArmor {
	pub id: Option<String>,
	pub name: Option<String>,
	pub assets: MatchesV2DataRoundPlayerStatsEconomyEquipmentAssetsArmor,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsEconomyEquipmentAssets {
	pub display_icon: Option<String>,
	pub killfeed_icon: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsEconomyEquipmentAssetsArmor {
	pub display_icon: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsAbilityCasts {
	pub x_casts: Option<u32>,
	pub e_casts: Option<u32>,
	pub q_casts: Option<u32>,
	pub c_casts: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsDamageEvents {
	pub receiver_puuid: String,
	pub receiver_display_name: String,
	pub receiver_team: String,
	pub bodyshots: u32,
	pub headshots: u32,
	pub legshots: u32,
	pub damage: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsKillEvents {
	pub kill_time_in_round: u64,
	pub kill_time_in_match: u64,
	pub killer_puuid: String,
	pub killer_display_name: String,
	pub killer_team: String,
	pub victim_puuid: String,
	pub victim_display_name: String,
	pub victim_team: String,
	pub victim_death_location: MatchesV2DataRoundEventLocation,
	pub damage_weapon_id: String,
	pub damage_weapon_name: Option<String>,
	pub damage_weapon_assets: MatchesV2DataRoundPlayerStatsKillEventsAssets,
	pub secondary_fire_mode: bool,
	pub player_locations_on_kill: Vec<MatchesV2DataRoundPlayerLocationsOnEvent>,
	pub assistants: Vec<MatchesV2DataRoundPlayerStatsKillEventsAssistants>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsKillEventsAssistants {
	pub assistant_puuid: String,
	pub assistant_display_name: String,
	pub assistant_team: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerStatsKillEventsAssets {
	pub display_icon: Option<String>,
	pub killfeed_icon: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlantEvents {
	pub plant_location: Option<MatchesV2DataRoundEventLocation>,
	pub planted_by: Option<MatchesV2DataRoundPlayer>,
	pub plant_site: Option<String>,
	pub plant_time_in_round: Option<u64>,
	pub player_locations_on_plant: Option<Vec<MatchesV2DataRoundPlayerLocationsOnEvent>>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundDefuseEvents {
	pub defuse_location: Option<MatchesV2DataRoundEventLocation>,
	pub defused_by: Option<MatchesV2DataRoundPlayer>,
	pub defuse_time_in_round: Option<u64>,
	pub player_locations_on_defuse: Option<Vec<MatchesV2DataRoundPlayerLocationsOnEvent>>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundEventLocation {
	pub x: i32,
	pub y: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayer {
	pub puuid: String,
	pub display_name: String,
	pub team: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct MatchesV2DataRoundPlayerLocationsOnEvent {
	pub player_puuid: String,
	pub player_display_name: String,
	pub player_team: String,
	pub location: MatchesV2DataRoundEventLocation,
	pub view_radians: f32,
}

//Premier
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonV1Response {
	pub status: u32,
	pub data: Vec<PremierSeasonV1ResponseData>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonV1ResponseData {
	pub id: String,
	pub championship_event_id: String,
	pub championship_points_required: i32,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub starts_at: DateTime,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub ends_at: DateTime,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub enrollment_starts_at: DateTime,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub enrollment_ends_at: DateTime,
	pub events: Vec<PremierSeasonV1ResponseDataEvent>,
	pub scheduled_events: Vec<PremierSeasonV1ResponseDataScheduledEvent>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonV1ResponseDataScheduledEvent {
	pub event_id: String,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub starts_at: DateTime,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub ends_at: DateTime,
	pub conference: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonV1ResponseDataEvent {
	pub id: String,
	#[serde(rename = "type")]
	pub type_: String,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub starts_at: DateTime,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub ends_at: DateTime,
	pub conference_schedules: Vec<PremierSeasonV1ResponseDataEventConferenceSchedule>,
	pub map_selection: PremierSeasonResponseV1DataEventMapSelection,
	pub points_required_to_participate: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonResponseV1DataEventMapSelection {
	#[serde(rename = "type")]
	pub type_: String,
	pub maps: Vec<PremierSeasonV1ResponseDataEventMapSelectionMaps>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonV1ResponseDataEventMapSelectionMaps {
	pub name: String,
	pub id: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierSeasonV1ResponseDataEventConferenceSchedule {
	pub conference: String,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub starts_at: DateTime,
	#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	pub ends_at: DateTime,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierConferenceResponse {
	pub status: u32,
	pub data: Vec<PremierConferenceResponseData>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierConferenceResponseData {
	pub id: String,
	pub affinity: String,
	pub pods: Vec<PremierConferenceResponseDataPods>,
	pub region: String,
	pub timezone: String,
	pub name: String,
	pub icon: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PremierConferenceResponseDataPods {
	pub pod: String,
	pub name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierSearchResponse {
	pub status: u32,
	pub data: Vec<PremierTeamLiteResponseData>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamLiteResponseData {
	pub id: String,
	pub name: String,
	pub tag: String,
	pub conference: String,
	pub division: i32,
	pub affinity: String,
	pub region: String,
	pub losses: i32,
	pub wins: i32,
	pub score: i32,
	pub ranking: i32,
	pub customization: PremierTeamV1ResponseDataCustomization,
	pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamV1Response {
	pub status: u32,
	pub data: PremierTeamV1ResponseData,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamV1ResponseData {
	pub id: String,
	pub name: String,
	pub tag: String,
	pub enrolled: bool,
	pub stats: PremierTeamV1ResponseDataStats,
	pub placement: PremierTeamV1ResponseDataPlacement,
	pub customization: PremierTeamV1ResponseDataCustomization,
	pub member: Vec<PremierTeamMember>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamV1ResponseDataStats {
	pub wins: i32,
	pub matches: i32,
	pub losses: i32,
	pub rounds_won: i32,
	pub rounds_lost: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamV1ResponseDataPlacement {
	pub points: i32,
	pub conference: String,
	pub division: i32,
	pub place: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamV1ResponseDataCustomization {
	pub icon: String,
	pub image: String,
	pub primary: String,
	pub secondary: String,
	pub tertiary: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamHistoryV1Response {
	pub status: u32,
	pub data: PremierTeamHistoryV1ResponseData,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct PremierTeamHistoryV1ResponseData {
	pub league_matches: Vec<PremierTeamGamesLeagueString>,
	pub tournament_matches: Vec<PremierTeamGamesTournament>,
}

//Assist Names
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AssistV1NamesResponse {
	pub puuid: String,
	pub name: Option<String>,
	pub tag: Option<String>,
	pub exists: bool,
}

//Queue Status
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct QueueStatusV1 {
	pub status: u32,
	pub data: Vec<QueueStatusV1Data>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1Data {
	pub mode: String,
	pub mode_id: String,
	pub enabled: bool,
	pub team_size: i32,
	pub number_of_teams: i32,
	pub party_size: QueueStatusV1PartySize,
	pub high_skill: QueueStatusV1HighSkill,
	pub ranked: bool,
	pub tournament: bool,
	pub skill_disparity: Vec<QueueStatusV1SkillDisparity>,
	pub required_account_level: i32,
	pub game_rules: QueueStatusV1GameRules,
	pub platforms: Vec<String>,
	pub maps: Vec<QueueStatusV1Maps>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1Maps {
	pub map: QueueStatusV1Map,
	pub enabled: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1Map {
	pub id: String,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1GameRules {
	pub overtime_win_by_two: bool,
	pub allow_lenient_surrender: bool,
	pub allow_drop_out: bool,
	pub assign_random_agents: bool,
	pub skip_pregame: bool,
	pub allow_overtime_draw_vote: bool,
	pub overtime_win_by_two_capped: bool,
	pub premier_mode: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1SkillDisparity {
	pub tier: i32,
	pub name: String,
	pub max_tier: QueueStatusV1IDNamePair,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1IDNamePair {
	pub id: usize,
	pub name: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1PartySize {
	pub min: i32,
	pub max: i32,
	pub invalid: Vec<i32>,
	pub full_party_bypass: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct QueueStatusV1HighSkill {
	pub max_party_size: i32,
	pub min_tier: i32,
	pub max_tier: i32,
}

//Status V1
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StatusV1 {
	pub status: u16,
	pub data: StatusV1Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StatusV1Data {
	pub maintenances: Vec<StatusIncident>,
	pub incidents: Vec<StatusIncident>,
}

//Store Featured V1
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1BundleItemOfferOfferReward {
	pub ItemTypeID: String,
	pub ItemID: String,
	pub Quantity: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1BundleItemOfferOffer {
	pub OfferID: String,
	pub IsDirectPurchase: bool,
	pub StartDate: String,
	pub Cost: HashMap<String, i32>,
	pub Rewards: Vec<StoreFeaturedV1BundleItemOfferOfferReward>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1BundleItemOffer {
	pub BundleItemOfferID: String,
	pub DiscountPercent: u32,
	pub DiscountedCost: HashMap<String, i32>,
	pub Offer: StoreFeaturedV1BundleItemOfferOffer,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1BundleItemDescription {
	pub ItemTypeID: String,
	pub ItemID: String,
	pub Amount: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1BundleItem {
	pub Item: StoreFeaturedV1BundleItemDescription,
	pub BasePrice: u32,
	pub CurrencyID: String,
	pub DiscountPercent: u32,
	pub DiscountedPrice: u32,
	pub IsPromoItem: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1Bundle {
	pub ID: String,
	pub DataAssetID: String,
	pub CurrencyID: String,
	pub Items: Vec<StoreFeaturedV1BundleItem>,
	pub ItemOffers: Option<Vec<StoreFeaturedV1BundleItemOffer>>,
	pub TotalBaseCost: Option<HashMap<String, u32>>,
	pub TotalDiscountedCost: Option<HashMap<String, u32>>,
	pub TotalDiscountPercent: f32,
	pub DurationRemainingInSeconds: u32,
	pub WholesaleOnly: bool,
	pub timestamp: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1Root {
	pub Bundle: StoreFeaturedV1Bundle,
	pub Bundles: Vec<StoreFeaturedV1Bundle>,
	pub BundleRemainingDurationInSeconds: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV1 {
	#[schema(value_type = crate::structs::utoipa_structs::FeaturedBundle)]
	pub FeaturedBundle: FeaturedBundle,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoreFeaturedV1Response {
	pub status: u32,
	pub data: StoreFeaturedV1,
}

//Store Featured V2
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StoreFeaturedV2Response {
	pub status: u32,
	pub data: Vec<StoreFeaturedV2>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV2 {
	pub bundle_uuid: String,
	pub bundle_price: u32,
	pub whole_sale_only: bool,
	pub items: Vec<StoreFeaturedV2Item>,
	pub seconds_remaining: u32,
	pub expires_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct StoreFeaturedV2Item {
	pub uuid: String,
	pub name: String,
	pub image: Option<String>,
	pub r#type: String,
	pub amount: u32,
	pub discount_percent: f32,
	pub base_price: i32,
	pub discounted_price: i32,
	pub promo_item: bool,
}

// Store Offers V1
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreOffersV1 {
	pub Offers: Vec<StoreOffersV1Offer>,
	pub UpgradeCurrencyOffers: Vec<StoreOffersV1UpgradeCurrency>,
}

#[allow(non_snake_case)]
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoreOffersV1UpgradeCurrency {
	pub OfferID: String,
	pub StorefrontItemID: String,
	pub Offer: StoreOffersV1Offer,
	pub DiscountedPercent: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreOffersV1Offer {
	pub OfferID: String,
	pub IsDirectPurchase: bool,
	pub StartDate: String,
	pub Cost: HashMap<String, i32>,
	pub Rewards: Vec<StoreOffersV1Reward>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StoreOffersV1Reward {
	pub ItemTypeID: String,
	pub ItemID: String,
	pub Quantity: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct StoreOffersV1Response {
	pub status: u32,
	pub data: StoreOffersV1,
}

// Store Offers V2
#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct StoreOffersV2OfferContentTier {
	pub name: String,
	pub dev_name: String,
	pub icon: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct StoreOffersV2Offer {
	pub offer_id: String,
	pub cost: u32,
	pub name: Option<String>,
	pub icon: Option<String>,
	pub r#type: String,
	pub skin_id: Option<String>,
	pub content_tier: Option<StoreOffersV2OfferContentTier>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StoreOffersV2Response {
	pub status: u32,
	pub data: Vec<StoreOffersV2Offer>,
}

//Version
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct VersionV1Data {
	pub region: String,
	pub branch: String,
	pub build_date: String,
	pub build_ver: String,
	pub last_checked: String,
	pub version: u32,
	pub version_for_api: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct VersionV1Response {
	pub status: u32,
	pub data: VersionV1Data,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct WebsiteV1Data {
	pub banner_url: Option<String>,
	pub description: Option<String>,
	pub category: String,
	pub date: String,
	pub external_link: Option<String>,
	pub title: String,
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct WebsiteV1Response {
	pub status: u32,
	pub data: Vec<WebsiteV1Data>,
}

// Raw endpoint responses
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct RawV1ErrorData {
	pub error: bool,
	pub code: u16,
	pub id: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[serde(untagged)]
pub enum RawV1ResponseData {
	Single(Value),
	Multiple(Vec<Value>),
	Error(RawV1ErrorData),
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct RawV1Response {
	pub status: u32,
	pub data: RawV1ResponseData,
}

// Content endpoint response
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct ContentV1Response {
	pub status: u32,
	#[schema(value_type = crate::structs::utoipa_structs::ContentV1)]
	pub data: valorant_api_official::response_types::content_v1::ContentV1,
}
