use redis::RedisResult;
use crate::structs::enums::RiotPlatforms;
use crate::{ ALTERNATIVE_PROXY_CLIENT, DEFAULT_CLIENT, IPV6_CLIENT };
use crate::{ build_riot_headers, B2_AUTH };
use log::{ error, info };
use redis::aio::MultiplexedConnection;
use reqwest::header::{ HeaderMap, HeaderValue, AUTHORIZATION };
use reqwest::{ Client as HTTPClient, ClientBuilder };
use serde::de::DeserializeOwned;
use serde_json::{ Value };
use std::collections::HashMap;
use redis::AsyncCommands;

#[derive(Debug, Clone)]
pub struct FetchResponse<T> {
	pub status: u16,
	pub headers: HeaderMap,
	pub url: String,
	pub data: T,
	pub pass_data: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone)]
pub struct RedisFetchResponse<T> {
	pub status: u16,
	pub headers: HeaderMap,
	pub url: String,
	pub data: T,
	pub is_from_redis: bool,
	pub pass_data: Option<HashMap<String, String>>,
	pub ttl: i16,
}

#[derive(Debug, Clone)]
pub struct FetchError {
	pub status: u16,
	pub headers: HeaderMap,
	pub url: String,
	pub error: String,
	pub error_type: String,
	pub data: Option<String>,
	pub value_data: Value,
}

#[derive(Debug, Clone)]
pub struct B2FetchResponse<T> {
	pub status: u16,
	pub headers: HeaderMap,
	pub url: String,
	pub data: T,
	pub pass_data: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone)]
pub struct B2RedisFetchResponse<T> {
	pub status: u16,
	pub headers: HeaderMap,
	pub url: String,
	pub data: T,
	pub is_from_redis: bool,
	pub pass_data: Option<HashMap<String, String>>,
	pub ttl: i16,
}

#[derive(Debug, Clone)]
pub struct HTMLFetchResponse {
	pub status: u16,
	pub headers: HeaderMap,
	pub url: String,
	pub data: String, // raw HTML/text
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct HTMLFetchOptions {
	pub url: String,
	pub method: String,
	pub headers: Option<HeaderMap>,
	pub user_agent: String,
	pub data: Value,
	pub alternative_proxy: bool,
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct RedisFetchOptions {
	pub url: String,
	pub method: String,
	pub headers: HeaderMap,
	pub user_agent: String,
	pub data: Value,
	pub store: String,
	pub alternative_proxy: bool,
	pub is_text: bool,
	pub redis_client: Option<MultiplexedConnection>,
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct RedisFetchIPv6Options {
	pub url: String,
	pub method: String,
	pub headers: HeaderMap,
	pub user_agent: String,
	pub data: Value,
	pub store: String,
	pub is_text: bool,
	pub redis_client: Option<MultiplexedConnection>,
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct FetchOptions {
	pub url: String,
	pub method: String,
	pub headers: Option<HeaderMap>,
	pub user_agent: String,
	pub data: Value,
	pub alternative_proxy: bool,
	pub is_text: bool,
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct FetchIPv6Options {
	pub url: String,
	pub method: String,
	pub headers: Option<HeaderMap>,
	pub user_agent: String,
	pub data: Value,
	pub is_text: bool,
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct B2FetchOptions {
	pub url: String,
	pub method: String,
	pub data: Value,
	pub use_b2_auth: bool,
	pub pass_data: Option<HashMap<String, String>>,
}

pub struct B2RedisFetchOptions {
	pub url: String,
	pub method: String,
	pub data: Value,
	pub store: String,
	pub redis_client: Option<MultiplexedConnection>,
	pub use_b2_auth: bool,
	pub custom_ttl: Option<i32>,
	pub pass_data: Option<HashMap<String, String>>,
}

impl Default for FetchOptions {
	fn default() -> Self {
		FetchOptions {
			url: String::from(""),
			method: String::from("GET"),
			headers: Some(HeaderMap::new()),
			user_agent: String::from("ValorantAPI/4.4.0"),
			data: Value::Null,
			alternative_proxy: false,
			is_text: false,
			pass_data: None,
		}
	}
}

impl Default for RedisFetchOptions {
	fn default() -> Self {
		RedisFetchOptions {
			url: String::from(""),
			method: String::from("GET"),
			headers: HeaderMap::new(),
			user_agent: String::from("ValorantAPI/4.4.0"),
			data: Value::Null,
			store: String::from(""),
			alternative_proxy: false,
			is_text: false,
			redis_client: None,
			pass_data: None,
		}
	}
}

impl Default for RedisFetchIPv6Options {
	fn default() -> Self {
		RedisFetchIPv6Options {
			url: String::from(""),
			method: String::from("GET"),
			headers: HeaderMap::new(),
			user_agent: String::from("ValorantAPI/4.4.0"),
			data: Value::Null,
			store: String::from(""),
			is_text: false,
			redis_client: None,
			pass_data: None,
		}
	}
}

impl Default for FetchIPv6Options {
	fn default() -> Self {
		FetchIPv6Options {
			url: String::from(""),
			method: String::from("GET"),
			headers: Some(HeaderMap::new()),
			user_agent: String::from("ValorantAPI/4.4.0"),
			data: Value::Null,
			is_text: false,
			pass_data: None,
		}
	}
}

impl Default for B2FetchOptions {
	fn default() -> Self {
		B2FetchOptions {
			url: String::from(""),
			method: String::from("GET"),
			data: Value::Null,
			use_b2_auth: false,
			pass_data: None,
		}
	}
}

impl Default for B2RedisFetchOptions {
	fn default() -> Self {
		B2RedisFetchOptions {
			url: String::from(""),
			method: String::from("GET"),
			data: Value::Null,
			store: String::from(""),
			redis_client: None,
			use_b2_auth: false,
			custom_ttl: None,
			pass_data: None,
		}
	}
}

impl Default for HTMLFetchOptions {
	fn default() -> Self {
		HTMLFetchOptions {
			url: String::from(""),
			method: String::from("GET"),
			headers: Some(HeaderMap::new()),
			user_agent: String::from("ValorantAPI/4.4.0"),
			data: Value::Null,
			alternative_proxy: false,
			pass_data: None,
		}
	}
}

/// Fetch raw HTML/text using the default or alternative proxy client. Does not attempt to parse JSON.
pub async fn fetch_html(options: HTMLFetchOptions) -> Result<HTMLFetchResponse, FetchError> {
	if cfg!(debug_assertions) {
		println!("[DEBUG] Fetching HTML: {}", options.url);
	}
	let client = if options.alternative_proxy { ALTERNATIVE_PROXY_CLIENT.get().unwrap() } else { DEFAULT_CLIENT.get().unwrap() };
	let mut req = match options.method.as_str() {
		"GET" => client.get(&options.url),
		"POST" => client.post(&options.url).body(options.data.to_string()),
		"PUT" => client.put(&options.url).body(options.data.to_string()),
		"DELETE" => client.delete(&options.url),
		_ => client.get(&options.url),
	};

	if let Some(headers) = options.headers {
		if headers.is_empty() {
			req = req.headers(build_riot_headers(&RiotPlatforms::PC).await);
		} else {
			req = req.headers(headers);
		}
	} else {
		req = req.headers(HeaderMap::new());
	}
	req = req.header("User-Agent", options.user_agent);

	let resp = req.send().await;
	match resp {
		Ok(v) => {
			let status = v.status().as_u16();
			let headers = v.headers().clone();
			let url = v.url().to_string();
			let text = v.text().await.unwrap_or_default();
			let response = HTMLFetchResponse {
				status,
				headers,
				url,
				data: text,
				pass_data: options.pass_data,
			};
			Ok(response)
		}
		Err(e) => {
			eprintln!("[FETCH_HTML] Error: {:?}", e);
			Err(FetchError {
				status: 500,
				headers: HeaderMap::new(),
				url: options.url.clone(),
				error: e.to_string(),
				error_type: String::from("HTTP_CLIENT"),
				data: None,
				value_data: Value::Null,
			})
		}
	}
}

/// IPv6 variant of the HTML-only client.
pub async fn fetch_html_ipv6(options: HTMLFetchOptions) -> Result<HTMLFetchResponse, FetchError> {
	if cfg!(debug_assertions) {
		println!("[DEBUG] Fetching HTML (IPv6): {}", options.url);
	}
	let client = IPV6_CLIENT.get().unwrap();
	let mut req = match options.method.as_str() {
		"GET" => client.get(&options.url),
		"POST" => client.post(&options.url).body(options.data.to_string()),
		"PUT" => client.put(&options.url).body(options.data.to_string()),
		"DELETE" => client.delete(&options.url),
		_ => client.get(&options.url),
	};

	if let Some(headers) = options.headers {
		if headers.is_empty() {
			req = req.headers(build_riot_headers(&RiotPlatforms::PC).await);
		} else {
			req = req.headers(headers);
		}
	} else {
		req = req.headers(build_riot_headers(&RiotPlatforms::PC).await);
	}
	req = req.header("User-Agent", options.user_agent);

	let resp = req.send().await;
	match resp {
		Ok(v) => {
			let status = v.status().as_u16();
			let headers = v.headers().clone();
			let url = v.url().to_string();
			let text = v.text().await.unwrap_or_default();
			let response = HTMLFetchResponse {
				status,
				headers,
				url,
				data: text,
				pass_data: options.pass_data,
			};
			Ok(response)
		}
		Err(e) => {
			eprintln!("[FETCH_HTML_IPV6] Error: {:?}", e);
			Err(FetchError {
				status: 500,
				headers: HeaderMap::new(),
				url: options.url.clone(),
				error: e.to_string(),
				error_type: String::from("HTTP_CLIENT"),
				data: None,
				value_data: Value::Null,
			})
		}
	}
}

pub async fn redis_fetch<T: DeserializeOwned + std::fmt::Debug + serde::Serialize + Clone>(options: RedisFetchOptions) -> Result<RedisFetchResponse<T>, FetchError> {
	let mut redis_con = options.redis_client.unwrap().clone();
	let redis_fetch: RedisResult<String> = redis_con.get(&options.store).await;
	match redis_fetch {
		Ok(v) => {
			let decode: Result<T, serde_json::Error> = serde_json::from_str::<T>(&v);
			if decode.is_err() {
				eprintln!("[DEBUG] Error Decoding Redis: {:?}", &options.url);
				let deserializer = &mut serde_json::Deserializer::from_str(&v);
				let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
				let path = error.as_ref().unwrap().path().to_string();
				if cfg!(debug_assertions) {
					eprintln!("[DEBUG] Error Decoding Redis: {:?}", &path);
				}
				return Err(FetchError {
					status: 500,
					headers: HeaderMap::new(),
					url: options.url.clone(),
					error: String::from("Error while parsing JSON"),
					error_type: String::from("PARSE_JSON"),
					data: Some(path),
					value_data: if let Ok(v) = serde_json::from_str(&v) {
						v
					} else {
						Value::Null
					},
				});
			}
			let ttl: i16 = redis_con.ttl(&options.store).await.unwrap_or(0);
			let response = RedisFetchResponse {
				status: 200,
				headers: options.headers.clone(),
				url: options.url,
				data: decode.unwrap(),
				is_from_redis: true,
				pass_data: options.pass_data,
				ttl,
			};
			return Ok(response);
		}
		Err(_) => {
			let c_fetch = fetch::<T>(FetchOptions {
				url: options.url.clone(),
				method: options.method,
				headers: Some(options.headers),
				user_agent: options.user_agent,
				data: options.data,
				alternative_proxy: options.alternative_proxy,
				is_text: options.is_text,
				pass_data: options.pass_data,
			}).await;
			match c_fetch {
				Ok(v) => {
					let string: String = serde_json::to_string(&v.data).unwrap();
					let _: RedisResult<()> = redis_con.set_ex(&options.store.clone(), string, 300).await;
					Ok(RedisFetchResponse {
						status: v.status,
						headers: v.headers,
						url: v.url,
						data: v.data,
						is_from_redis: false,
						pass_data: v.pass_data,
						ttl: 300,
					})
				}
				Err(e) =>
					Err(FetchError {
						status: e.status,
						headers: e.headers,
						url: e.url,
						error: e.error,
						error_type: e.error_type,
						data: e.data,
						value_data: e.value_data,
					}),
			}
		}
	}
}

pub async fn redis_fetch_ipv6<T: DeserializeOwned + std::fmt::Debug + serde::Serialize + Clone>(
	options: RedisFetchIPv6Options
) -> Result<RedisFetchResponse<T>, FetchError> {
	let mut redis_con = options.redis_client.unwrap().clone();
	let redis_fetch: RedisResult<String> = redis_con.get(&options.store).await;
	match redis_fetch {
		Ok(v) => {
			let decode: Result<T, serde_json::Error> = serde_json::from_str::<T>(&v);
			if decode.is_err() {
				let deserializer = &mut serde_json::Deserializer::from_str(&v);
				let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
				let path = error.as_ref().unwrap().path().to_string();
				eprintln!("[DEBUG] Error Decoding Redis: {:?}", &options.url);
				eprintln!("[DEBUG] Error Decoding Redis: {:?}", &path);
				eprintln!("Error: {:?}", &v);
				return Err(FetchError {
					status: 500,
					headers: HeaderMap::new(),
					url: options.url.clone(),
					error: String::from("Error while parsing JSON"),
					error_type: String::from("PARSE_JSON"),
					data: Some(path),
					value_data: if let Ok(v) = serde_json::from_str(&v) {
						v
					} else {
						Value::Null
					},
				});
			}
			let ttl: i16 = redis_con.ttl(&options.store).await.unwrap_or(0);
			let response = RedisFetchResponse {
				status: 200,
				headers: HeaderMap::new(),
				url: options.url,
				data: decode.unwrap(),
				is_from_redis: true,
				pass_data: options.pass_data,
				ttl,
			};
			Ok(response)
		}
		Err(_) => {
			let c_fetch = fetch_ipv6::<T>(FetchIPv6Options {
				url: options.url.clone(),
				method: options.method,
				headers: Some(options.headers),
				user_agent: options.user_agent,
				data: options.data,
				is_text: options.is_text,
				pass_data: options.pass_data,
			}).await;
			match c_fetch {
				Ok(v) => {
					if cfg!(debug_assertions) {
						println!("[DEBUG] Fetching: {:?}", &v.data);
					}
					let string: String = serde_json::to_string(&v.data).unwrap();
					let _: RedisResult<()> = redis_con.set_ex(&options.store.clone(), string, 300).await;
					Ok(RedisFetchResponse {
						status: v.status,
						headers: v.headers,
						url: v.url,
						data: v.data,
						is_from_redis: false,
						pass_data: v.pass_data,
						ttl: 300,
					})
				}
				Err(e) =>
					Err(FetchError {
						status: e.status,
						headers: e.headers,
						url: e.url,
						error: e.error,
						error_type: e.error_type,
						data: e.data,
						value_data: e.value_data,
					}),
			}
		}
	}
}

pub async fn fetch_ipv6<T: DeserializeOwned + std::fmt::Debug + Clone>(options: FetchIPv6Options) -> Result<FetchResponse<T>, FetchError> {
	if cfg!(debug_assertions) {
		println!("[DEBUG] Fetching: {}", options.url);
	}
	let client = IPV6_CLIENT.get().unwrap();
	let mut req = match options.method.as_str() {
		"GET" => client.get(&options.url),
		"POST" => client.post(&options.url).json(&options.data),
		"PUT" => client.put(&options.url).json(&options.data),
		"DELETE" => client.delete(&options.url),
		_ => client.get(&options.url),
	};
	if let Some(headers) = options.headers {
		if headers.is_empty() {
			req = req.headers(build_riot_headers(&RiotPlatforms::PC).await);
		} else {
			req = req.headers(headers);
		}
	} else {
		req = req.headers(build_riot_headers(&RiotPlatforms::PC).await);
	}
	req = req.header("User-Agent", options.user_agent);

	let resp = req.send().await;
	match resp {
		Ok(v) => {
			let status = v.status().as_u16();
			let headers = v.headers().clone();
			let url = v.url().to_string();
			let text = v.text().await.unwrap();
			let decode = serde_json::from_str::<T>(&text);
			if decode.is_err() {
				let deserializer = &mut serde_json::Deserializer::from_str(&text);
				let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
				let path = error.as_ref().unwrap().path().to_string();
				if cfg!(debug_assertions) {
					eprintln!("[DEBUG] Error Decoding: {:?}", &url);
					eprintln!("[DEBUG] Error Decoding: {:?}", &headers);
					eprintln!("Error: {:?}", &text);
				}
				return Err(FetchError {
					status,
					headers: HeaderMap::new(),
					url: url.clone(),
					error: String::from("Error while parsing JSON"),
					error_type: String::from("PARSE_JSON"),
					data: Some(path),
					value_data: if let Ok(v) = serde_json::from_str(&text) {
						v
					} else {
						Value::Null
					},
				});
			}
			let response = FetchResponse {
				status,
				headers,
				url,
				data: decode.unwrap(),
				pass_data: options.pass_data,
			};
			Ok(response)
		}
		Err(e) => {
			eprintln!("[FETCH] Error: {:?}", e);
			Err(FetchError {
				status: 500,
				headers: HeaderMap::new(),
				url: options.url.clone(),
				error: e.to_string(),
				error_type: String::from("HTTP_CLIENT"),
				data: None,
				value_data: Value::Null,
			})
		}
	}
}

pub async fn fetch<T: DeserializeOwned + std::fmt::Debug + Clone>(options: FetchOptions) -> Result<FetchResponse<T>, FetchError> {
	if cfg!(debug_assertions) {
		println!("[DEBUG] Fetching: {}", options.url);
	}
	let client = if options.alternative_proxy { ALTERNATIVE_PROXY_CLIENT.get().unwrap() } else { DEFAULT_CLIENT.get().unwrap() };
	let mut req = match options.method.as_str() {
		"GET" => client.get(&options.url),
		"POST" => client.post(&options.url).json(&options.data),
		"PUT" => client.put(&options.url).json(&options.data),
		"DELETE" => client.delete(&options.url),
		_ => client.get(&options.url),
	};
	if let Some(headers) = options.headers {
		if headers.is_empty() {
			req = req.headers(build_riot_headers(&RiotPlatforms::PC).await);
		} else {
			req = req.headers(headers);
		}
	} else {
		req = req.headers(HeaderMap::new());
	}
	req = req.header("User-Agent", options.user_agent);
	let resp = req.send().await;
	match resp {
		Ok(v) => {
			let status = v.status().as_u16();
			let headers = v.headers().clone();
			let url = v.url().to_string();
			let text = v.text().await.unwrap();
			let decode = serde_json::from_str::<T>(&text);
			if decode.is_err() {
				let deserializer = &mut serde_json::Deserializer::from_str(&text);
				let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
				let path = error.as_ref().unwrap().path().to_string();
				if cfg!(debug_assertions) {
					eprintln!("[DEBUG] Error Decoding: {:?}", &url);
					eprintln!("[DEBUG] Error Decoding: {:?}", &path);
				}
				return Err(FetchError {
					status,
					headers: HeaderMap::new(),
					url: url.clone(),
					error: String::from("Error while parsing JSON"),
					error_type: String::from("PARSE_JSON"),
					data: Some(path),
					value_data: if let Ok(v) = serde_json::from_str(&text) {
						v
					} else {
						Value::Null
					},
				});
			}
			let response = FetchResponse {
				status,
				headers,
				url,
				data: decode.unwrap(),
				pass_data: options.pass_data,
			};
			Ok(response)
		}
		Err(e) => {
			eprintln!("[FETCH] Error: {:?}", e);
			Err(FetchError {
				status: 500,
				headers: HeaderMap::new(),
				url: options.url.clone(),
				error: e.to_string(),
				error_type: String::from("HTTP_CLIENT"),
				data: None,
				value_data: Value::Null,
			})
		}
	}
}

pub async fn b2_redis_fetch<T: DeserializeOwned + std::fmt::Debug + serde::Serialize + Clone>(
	options: B2RedisFetchOptions
) -> Result<B2RedisFetchResponse<T>, FetchError> {
	let mut redis_con = options.redis_client.unwrap().clone();
	let redis_fetch: RedisResult<String> = redis_con.get(&options.store).await;

	match redis_fetch {
		Ok(v) => {
			let decode: Result<T, serde_json::Error> = serde_json::from_str::<T>(&v);
			if decode.is_err() {
				error!("[B2_REDIS] Error Decoding Redis: {:?}", &options.url);
				let deserializer = &mut serde_json::Deserializer::from_str(&v);
				let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
				let path = error.as_ref().unwrap().path().to_string();
				if cfg!(debug_assertions) {
					error!("[B2_REDIS] Error Decoding Redis: {:?}", &path);
				}
				return Err(FetchError {
					status: 500,
					headers: HeaderMap::new(),
					url: options.url.clone(),
					error: String::from("Error while parsing JSON"),
					error_type: String::from("PARSE_JSON"),
					data: Some(path),
					value_data: if let Ok(v) = serde_json::from_str(&v) {
						v
					} else {
						Value::Null
					},
				});
			}
			let ttl: i16 = redis_con.ttl(&options.store).await.unwrap_or(0);
			let response = B2RedisFetchResponse {
				status: 200,
				headers: HeaderMap::new(),
				url: options.url,
				data: decode.unwrap(),
				is_from_redis: true,
				pass_data: options.pass_data,
				ttl,
			};
			return Ok(response);
		}
		Err(_) => {
			let c_fetch = b2_fetch::<T>(B2FetchOptions {
				url: options.url.clone(),
				method: options.method,
				data: options.data,
				use_b2_auth: options.use_b2_auth,
				pass_data: options.pass_data,
			}).await;
			match c_fetch {
				Ok(v) => {
					let string: String = serde_json::to_string(&v.data).unwrap();
					let ttl = options.custom_ttl.unwrap_or(300) as u64;
					let _: RedisResult<()> = redis_con.set_ex(&options.store.clone(), string, ttl).await;
					Ok(B2RedisFetchResponse {
						status: v.status,
						headers: v.headers,
						url: v.url,
						data: v.data,
						is_from_redis: false,
						pass_data: v.pass_data,
						ttl: ttl as i16,
					})
				}
				Err(e) =>
					Err(FetchError {
						status: e.status,
						headers: e.headers,
						url: e.url,
						error: e.error,
						error_type: e.error_type,
						data: e.data,
						value_data: e.value_data,
					}),
			}
		}
	}
}

pub async fn b2_fetch<T: DeserializeOwned + std::fmt::Debug + Clone>(options: B2FetchOptions) -> Result<B2FetchResponse<T>, FetchError> {
	if cfg!(debug_assertions) {
		info!("[B2_FETCH] Fetching: {}", options.url);
	}

	let mut http_client: ClientBuilder = HTTPClient::builder();
	http_client = http_client.user_agent("ValorantAPI/4.4.0");

	// Build headers
	let mut headers = HeaderMap::new();
	if options.use_b2_auth {
		let b2_auth = B2_AUTH.load_full();
		if !b2_auth.authorization_token.is_empty() {
			headers.insert(AUTHORIZATION, HeaderValue::from_str(&b2_auth.authorization_token).unwrap());
		}
	}

	http_client = http_client.default_headers(headers);
	let http_client_build = match http_client.build() {
		Ok(v) => v,
		Err(e) => {
			error!("[B2_FETCH] Error building HTTP client: {:?}", e);
			return Err(FetchError {
				status: 500,
				headers: HeaderMap::new(),
				url: options.url,
				error: String::from("Error while building HTTP Client"),
				error_type: String::from("HTTP_CLIENT"),
				data: None,
				value_data: Value::Null,
			});
		}
	};

	let body = match options.method.as_str() {
		"GET" => http_client_build.get(options.url.clone()).send().await,
		"POST" => http_client_build.post(options.url.clone()).json(&options.data).send().await,
		"PUT" => http_client_build.put(options.url.clone()).json(&options.data).send().await,
		"DELETE" => http_client_build.delete(options.url.clone()).send().await,
		_ => http_client_build.get(options.url.clone()).send().await,
	};

	match body {
		Ok(v) => {
			let status = v.status().as_u16();
			let headers = v.headers().clone();
			let url = v.url().to_string();

			// Handle compressed data for B2 downloads
			let bytes = v.bytes().await.unwrap();

			// Try to decompress if it's compressed data
			let decompressed_data = if url.contains("cdn-b2.henrikdev.xyz") || url.contains("matches/") {
				match zstd::stream::decode_all(&bytes[..]) {
					Ok(decompressed) => decompressed,
					Err(_) => bytes.to_vec(), // If decompression fails, use raw bytes
				}
			} else {
				bytes.to_vec()
			};

			let text = String::from_utf8_lossy(&decompressed_data);
			let decode = serde_json::from_str::<T>(&text);

			if decode.is_err() {
				let deserializer = &mut serde_json::Deserializer::from_str(&text);
				let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
				let path = error.as_ref().unwrap().path().to_string();
				if cfg!(debug_assertions) {
					error!("[B2_FETCH] Error Decoding: {:?}", &url);
					error!("[B2_FETCH] Error Decoding: {:?}", &path);
				}
				return Err(FetchError {
					status,
					headers: HeaderMap::new(),
					url: url.clone(),
					error: String::from("Error while parsing JSON"),
					error_type: String::from("PARSE_JSON"),
					data: Some(path),
					value_data: if let Ok(v) = serde_json::from_str(&text) {
						v
					} else {
						Value::Null
					},
				});
			}
			let response = B2FetchResponse {
				status,
				headers,
				url,
				data: decode.unwrap(),
				pass_data: options.pass_data,
			};
			Ok(response)
		}
		Err(e) => {
			error!("[B2_FETCH] Error: {:?}", e);
			Err(FetchError {
				status: 500,
				headers: HeaderMap::new(),
				url: options.url.clone(),
				error: e.to_string(),
				error_type: String::from("HTTP_CLIENT"),
				data: None,
				value_data: Value::Null,
			})
		}
	}
}
