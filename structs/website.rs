use mongodb::bson::DateTime;
use serde::{ Deserialize, Serialize };
use serde_json::Value;
use crate::structs::database::c_deserialize;
use crate::structs::external::OpenRouterNewsExtract;

//Rito Mobile
#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNews {
	pub data: RitoMobileNewsData,
	pub metadata: RitoMobileNewsMetadata,
	pub links: RitoMobileNewsLinks,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsMetadata {
	pub total_items: i32,
	pub locale: String,
	pub from: i32,
	pub to: i32,
	#[serde(deserialize_with = "c_deserialize")]
	pub results_updated_at: DateTime,
	pub multigame_promo_channel_id: String,
	pub multigame_content_group_id: String,
	pub num_promoted_items: i32,
	pub total_pages: i32,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsLinks {
	pub self_: String,
	pub next: Option<String>,
	pub last: String,
	pub first: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsData {
	pub items: Vec<RitoMobileNewsElement>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElement {
	pub id: String,
	pub db_id: String,
	pub product: RitoMobileNewsElementProduct,
	pub headline: String,
	pub description: Option<String>,
	pub authors: Vec<String>,
	pub feature_image: RitoMobileNewsElementFeatureImage,
	pub render_type: String,
	pub render_options: RitoMobileNewsElementRenderOptions,
	pub promoted: bool,
	pub send_mobile_push_notification: bool,
	pub send_to_mobile: bool,
	pub hide_from_news_feed: bool,
	pub action: RitoMobileNewsElementAction,
	pub video_length_in_seconds: i32,
	pub category: RitoMobileNewsElementCategory,
	#[serde(deserialize_with = "c_deserialize")]
	pub published_at: DateTime,
	#[serde(deserialize_with = "c_deserialize")]
	pub updated_at: DateTime,
	pub cms: RitoMobileNewsElementCms,
	pub ai_extract: Option<OpenRouterNewsExtract>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementProduct {
	pub id: String,
	pub icon: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementFeatureImage {
	pub primary_color: Option<String>,
	pub secondary_color: Option<String>,
	pub label_color: Option<String>,
	#[serde(rename = "type")]
	pub type_: String,
	pub url: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementRenderOptions {
	pub size: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementAction {
	pub url: String,
	#[serde(rename = "type")]
	pub type_: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementCategory {
	pub id: String,
	pub title: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementCms {
	pub stack: String,
	pub source: RitoMobileNewsElementCmsSource,
	pub content_type: String,
	pub version: i32,
	pub locale: String,
	pub original_locale: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RitoMobileNewsElementCmsSource {
	pub source_system: String,
	pub source_dataset_name: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PlayValorantNews {
	pub componentChunkName: String,
	pub path: String,
	pub result: Result,
	pub staticQueryHashes: Vec<String>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct Result {
	pub data: Data,
	pub pageContext: PageContext,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Data {
	pub locales: Locales,
	pub contentstackHomepage: ContentstackHomepage,
	pub contentstackMetadata: ContentstackMetadata,
	pub allContentstackArticles: AllContentstackArticles,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Locales {
	pub edges: Vec<Edge>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Edge {
	pub node: Node,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Node {
	pub ns: String,
	pub data: String,
	pub language: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct ContentstackHomepage {
	pub uid: String,
	pub locale: String,
	pub news: News,
	pub headerModule: HeaderModule,
	pub latestEpisodeOrAct: LatestEpisodeOrAct,
	pub gameplayModule: GameplayModule,
	pub mapsModule: MapsModule,
	pub agentsModule: AgentsModule,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct News {
	pub headline: String,
	pub cta: Cta,
	pub content_type: String,
	pub articles_select: Vec<Value>,
	pub category_select: Vec<Value>,
	pub tag_select: Vec<Value>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Cta {
	pub title: String,
	pub href: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct HeaderModule {
	pub heroVideo: Vec<HeroVideo>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct HeroVideo {
	pub video: Video,
	pub static_image: StaticImage,
	pub alternate_videos: Vec<Value>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Video {
	pub file: File,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct File {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct StaticImage {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct LatestEpisodeOrAct {
	pub title: String,
	pub subtitle: String,
	pub cta: Cta2,
	pub background: Background,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Cta2 {
	pub href: String,
	pub title: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Background {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct GameplayModule {
	pub title: String,
	pub subtitle: String,
	pub description: String,
	pub cta: Cta3,
	pub video: Video2,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Cta3 {
	pub title: String,
	pub href: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Video2 {
	pub youtube_id: String,
	pub label: String,
	pub disclaimer: String,
	pub preview: Vec<Preview>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Preview {
	pub video: Video3,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Video3 {
	pub file: File2,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct File2 {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct MapsModule {
	pub background: Background2,
	pub cta: Cta4,
	pub description: String,
	pub subtitle: String,
	pub title: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Background2 {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Cta4 {
	pub title: String,
	pub href: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct AgentsModule {
	pub content_type: String,
	pub title: String,
	pub subtitle: String,
	pub description: String,
	pub cta: Cta5,
	pub agent_image: Option<AgentImage>,
	pub agent_video: Vec<AgentVideo>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Cta5 {
	pub title: String,
	pub href: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct AgentImage {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct AgentVideo {
	pub video: Video4,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Video4 {
	pub file: File3,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct File3 {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct ContentstackMetadata {
	pub id: String,
	pub title: String,
	pub opengraph: Opengraph,
	pub twitter: Twitter,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Opengraph {
	pub description: Option<String>,
	pub title: Option<String>,
	pub image: Image,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Image {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Twitter {
	pub description: Option<String>,
	pub title: Option<String>,
	pub image: Image2,
	pub site: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Image2 {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct AllContentstackArticles {
	pub nodes: Vec<Node2>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct Node2 {
	pub id: String,
	pub uid: String,
	pub title: String,
	pub date: String,
	pub article_type: String,
	pub external_link: String,
	pub article_tags: Option<Vec<ArticleTag>>,
	pub category: Vec<Category>,
	pub banner: Option<Banner>,
	pub url: Url,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct ArticleTag {
	pub title: String,
	pub machine_name: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct Category {
	pub title: String,
	pub machine_name: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct Banner {
	pub url: String,
	pub dimension: Dimension,
	pub content_type: String,
	pub file_size: String,
	pub filename: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct Dimension {
	pub height: i64,
	pub width: i64,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct Url {
	pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PageContext {
	pub language: String,
	pub i18n: I18n,
	pub environment: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct I18n {
	pub language: String,
	pub languages: Vec<String>,
	pub defaultLanguage: String,
	pub generateDefaultLanguagePage: bool,
	pub routed: bool,
	pub originalPath: String,
	pub path: String,
}
