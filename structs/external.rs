use std::collections::HashMap;
use serde::{ Deserialize, Serialize };

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct Proxys {
	pub count: i32,
	pub next: Option<String>,
	pub previous: Option<String>,
	pub results: Vec<Proxy>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct Proxy {
	pub id: String,
	pub username: String,
	pub password: String,
	pub proxy_address: String,
	pub port: i32,
	pub valid: bool,
	pub last_verification: String,
	pub country_code: String,
	pub city_name: String,
	pub asn_name: String,
	pub asn_number: i32,
	pub high_country_confidence: bool,
	pub created_at: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct B2Auth {
	pub authorization_token: String,
	pub api_info: B2ApiInfo,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct B2ApiInfo {
	pub storage_api: B2ApiStorageApi,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct B2ApiStorageApi {
	pub api_url: String,
	pub s3_api_url: String,
	pub bucket_id: String,
	pub bucket_name: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct OpenRouterNewsExtract {
	pub title: String,
	pub sub_title: String,
	pub content_md: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct CompletionResponse {
	pub id: String,
	pub choices: Vec<Choice>,
	pub provider: String,
	pub model: String,
	pub object: String,
	pub created: i64,
	pub usage: Usage,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct Choice {
	pub message: Message,
	pub logprobs: Option<HashMap<String, String>>,
	pub finish_reason: String,
	pub index: u32,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct Message {
	pub role: String,
	pub content: String,
	pub refusal: Option<String>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct Usage {
	pub prompt_tokens: u32,
	pub completion_tokens: u32,
	pub total_tokens: u32,
}
